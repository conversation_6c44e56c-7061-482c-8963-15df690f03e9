<?php
require_once '../template/header.php';
require_once '../models/Absensi.php';
require_once '../models/PeriodeAktif.php';

// Get active period
$periode = new PeriodeAktif();
if (!$periode->getActive()) {
    echo "<div class='alert alert-warning'>Tidak ada periode aktif saat ini. Silahkan set periode aktif terlebih dahulu.</div>";
    require_once '../template/footer.php';
    exit;
}

// Get semester and tahun ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : $periode->semester;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periode->tahun_ajaran;

// Check if siswa_id is provided
if (!isset($_GET['siswa_id'])) {
    echo "<div class='alert alert-danger'>Parameter siswa tidak ditemukan.</div>";
    require_once '../template/footer.php';
    exit;
}

$absensi = new Absensi();
$detail_data = $absensi->getRekapDetailSiswa($_GET['siswa_id'], $semester, $tahun_ajaran);

// Get first row for student info
$student_info = $detail_data->fetch(PDO::FETCH_ASSOC);
if (!$student_info) {
    echo "<div class='alert alert-warning'>Data siswa tidak ditemukan.</div>";
    require_once '../template/footer.php';
    exit;
}
// Reset pointer to beginning
$detail_data->execute();
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Detail Absensi Siswa</h2>
        <div>
            <a href="export_excel.php?<?php echo http_build_query($_GET); ?>" class="btn btn-success">
                <i class="fas fa-file-excel"></i> Export Excel
            </a>
            <a href="export_pdf.php?<?php echo http_build_query($_GET); ?>" class="btn btn-danger" target="_blank">
                <i class="fas fa-file-pdf"></i> Export PDF
            </a>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th width="150">Nama Siswa</th>
                            <td>: <?php echo $student_info['nama_siswa']; ?></td>
                        </tr>
                        <tr>
                            <th>Kelas</th>
                            <td>: <?php echo $student_info['nama_kelas']; ?></td>
                        </tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <table class="table table-borderless">
                        <tr>
                            <th width="150">Periode</th>
                            <td>: <?php echo $semester . ' - ' . $tahun_ajaran; ?></td>
                        </tr>
                        <?php if($semester != $periode->semester || $tahun_ajaran != $periode->tahun_ajaran): ?>
                        <tr>
                            <th>Catatan</th>
                            <td>: <span class="text-info">Anda sedang melihat data periode yang berbeda dari periode aktif</span></td>
                        </tr>
                        <?php endif; ?>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Riwayat Absensi</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table id="absensi-table" class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Tanggal</th>
                            <th>Status</th>
                            <th>Mata Pelajaran</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        while ($row = $detail_data->fetch(PDO::FETCH_ASSOC)) :
                            $status_badge = '';
                            $tanggal_absensi = $row['tanggal_absensi'] ?? '1970-01-01';
                            $status = $row['status'] ?? '';
                            $mapel = $row['nama_mapel'] ?? '';

                            switch($status) {
                                case 'Hadir':
                                    $status_badge = 'success';
                                    $status_text = 'Hadir';
                                    break;
                                case 'Sakit':
                                    $status_badge = 'warning';
                                    $status_text = 'Sakit';
                                    break;
                                case 'Izin':
                                    $status_badge = 'info';
                                    $status_text = 'Izin';
                                    break;
                                case 'Alpha':
                                    $status_badge = 'danger';
                                    $status_text = 'Alpha';
                                    break;
                                default:
                                    $status_badge = 'secondary';
                                    $status_text = 'Unknown';
                            }
                        ?>
                        <tr>
                            <td><?php echo $no++; ?></td>
                            <td><?php echo date('d/m/Y', strtotime($tanggal_absensi)); ?></td>
                            <td><span class="badge bg-<?php echo $status_badge; ?>"><?php echo $status_text; ?></span></td>
                            <td><?php echo $mapel; ?></td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#absensi-table').DataTable({
        "pageLength": 25,
        "order": [[1, 'desc']], // Sort by date descending
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
