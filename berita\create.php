<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../template/header.php';
require_once '../models/Berita.php';
require_once '../config/database.php';

$database = new Database();
$berita = new Berita();
$error = "";
$success = "";

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $berita->judul = $_POST['judul'];
    $berita->isi = $_POST['isi'];
    $berita->created_by = $_SESSION['user_id'];

    // Handle thumbnail upload
    if (isset($_FILES['thumbnail']) && $_FILES['thumbnail']['error'] == 0) {
        $allowed = ['jpg', 'jpeg', 'png', 'gif'];
        $filename = $_FILES['thumbnail']['name'];
        $ext = strtolower(pathinfo($filename, PATHINFO_EXTENSION));

        if (in_array($ext, $allowed)) {
            $new_filename = uniqid() . '.' . $ext;
            $upload_path = '../uploads/berita/' . $new_filename;

            if (move_uploaded_file($_FILES['thumbnail']['tmp_name'], $upload_path)) {
                $berita->thumbnail = $new_filename;
            } else {
                $error = "Gagal mengupload thumbnail";
            }
        } else {
            $error = "Format file tidak diizinkan. Gunakan: " . implode(', ', $allowed);
        }
    }

    if (empty($error)) {
        if ($berita->create()) {
            $success = "Berita berhasil ditambahkan";
            header("Location: index.php?success=1");
            exit();
        } else {
            $error = "Gagal menambahkan berita";
        }
    }
}

$page_title = "Tambah Berita";
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Tambah Berita</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="index.php">Berita</a></li>
                        <li class="breadcrumb-item active">Tambah</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <?php if ($error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>
            <?php if ($success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Form Tambah Berita</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <form action="" method="POST" enctype="multipart/form-data">
                        <div class="form-group mb-3">
                            <label for="judul">Judul Berita</label>
                            <input type="text" class="form-control" id="judul" name="judul" required>
                        </div>
                        <div class="form-group mb-3">
                            <label for="isi">Isi Berita</label>
                            <textarea class="form-control" id="isi" name="isi" rows="10" required></textarea>
                        </div>
                        <div class="form-group mb-3">
                            <label for="thumbnail">Thumbnail</label>
                            <input type="file" class="form-control" id="thumbnail" name="thumbnail">
                            <small class="form-text text-muted">Format yang diizinkan: JPG, JPEG, PNG, GIF</small>
                        </div>
                        <hr>
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
</div>

<script src="https://cdn.tiny.cloud/1/<?php echo $database->getTinyMCEApiKey(); ?>/tinymce/6/tinymce.min.js" referrerpolicy="origin"></script>
<script>
$(document).ready(function() {
    // Initialize TinyMCE with complete configuration
    tinymce.init({
        selector: '#isi',
        plugins: 'advlist autolink lists link image charmap print preview hr anchor pagebreak searchreplace wordcount visualblocks visualchars code fullscreen insertdatetime media nonbreaking save table contextmenu directionality emoticons template paste textcolor',
        toolbar: 'undo redo | styleselect | bold italic | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent | link image media | forecolor backcolor emoticons',
        toolbar_mode: 'floating',
        height: 300,
        setup: function(editor) {
            editor.on('change', function() {
                editor.save();
            });
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var judul = $('#judul').val().trim();
        var isi = tinymce.get('isi').getContent().trim();

        if (!judul) {
            e.preventDefault();
            alert('Judul berita tidak boleh kosong');
            return false;
        }

        if (!isi) {
            e.preventDefault();
            alert('Isi berita tidak boleh kosong');
            return false;
        }
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
