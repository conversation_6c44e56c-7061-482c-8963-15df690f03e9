<?php
require_once '../template/header.php';
require_once '../models/Absensi.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Guru.php';
require_once '../models/User.php';
require_once '../models/TahunAjaran.php';

// Check if user is logged in and is a guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: /absen/403.php");
    exit();
}

// Get guru_id from user_id
$user = new User();
$guru_id = $user->getGuruId($_SESSION['user_id']);

if (!$guru_id) {
    $_SESSION['error'] = "Data guru tidak ditemukan.";
    header("Location: /absen/index.php");
    exit();
}

// Check if guru is wali kelas
$guru = new Guru();
if (!$guru->isWaliKelas($guru_id)) {
    header("Location: /absen/403.php");
    exit();
}

// Get active period
$periode = new PeriodeAktif();
if (!$periode->getActive()) {
    echo "<div class='alert alert-warning'>Tidak ada periode aktif saat ini. Silahkan set periode aktif terlebih dahulu.</div>";
    require_once '../template/footer.php';
    exit;
}

// Get semester and tahun ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : null;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : null;

// If no semester/tahun_ajaran selected, use active period
if ($semester === null || $tahun_ajaran === null) {
    $semester = $periode->semester;
    $tahun_ajaran = $periode->tahun_ajaran;
}

// Get available tahun ajaran
$ta = new TahunAjaran();
$tahun_ajaran_list = $ta->getAllAsArray();

$absensi = new Absensi();

// Get class list - only show classes where the guru is wali kelas
$kelas_list = $guru->getKelasAsWaliKelas($guru_id);

// Get selected class - verify it belongs to this wali kelas
$selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
if ($selected_kelas) {
    $valid_kelas = false;
    $kelas_list_check = $guru->getKelasAsWaliKelas($guru_id);
    while ($row = $kelas_list_check->fetch(PDO::FETCH_ASSOC)) {
        if ($row['id'] == $selected_kelas) {
            $valid_kelas = true;
            break;
        }
    }
    if (!$valid_kelas) {
        header("Location: /absen/403.php");
        exit();
    }
}

// Get rekap data - only if a class is selected
$rekap_data = $selected_kelas ? $absensi->getRekapByPeriode($selected_kelas, $semester, $tahun_ajaran) : null;
?>

<div class="container-fluid">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">Filter Data</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="semester" class="form-label">Semester</label>
                    <select name="semester" id="semester" class="form-select">
                        <option value="1" <?php echo $semester == '1' ? 'selected' : ''; ?>>Semester 1</option>
                        <option value="2" <?php echo $semester == '2' ? 'selected' : ''; ?>>Semester 2</option>
                    </select>
                    <div class="form-text">
                        <i class="fas fa-info-circle"></i> Anda dapat memilih semester apapun untuk melihat rekap
                    </div>
                </div>
                <div class="col-md-3">
                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                    <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                        <?php foreach($tahun_ajaran_list as $ta): ?>
                            <option value="<?php echo $ta['tahun_ajaran']; ?>" <?php echo $tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : ''; ?>>
                                <?php echo $ta['tahun_ajaran']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <div class="form-text">
                        <i class="fas fa-info-circle"></i> Anda dapat memilih tahun ajaran apapun untuk melihat rekap
                    </div>
                </div>
                <div class="col-md-4">
                    <label for="kelas_id" class="form-label">Kelas</label>
                    <select name="kelas_id" id="kelas_id" class="form-select" required>
                        <option value="">Pilih Kelas</option>
                        <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) : ?>
                            <option value="<?php echo $row['id']; ?>" <?php echo $selected_kelas == $row['id'] ? 'selected' : ''; ?>>
                                <?php echo $row['nama_kelas']; ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">Tampilkan</button>
                </div>
            </form>
        </div>
    </div>

    <?php if ($selected_kelas && $rekap_data): ?>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Rekap Absensi Periode <?php echo $semester . ' - ' . $tahun_ajaran; ?></h5>
            <?php if($semester != $periode->semester || $tahun_ajaran != $periode->tahun_ajaran): ?>
                <div class="alert alert-info mb-0 py-1">
                    <small><i class="fas fa-info-circle"></i> Anda sedang melihat rekap periode: Semester <?php echo $semester; ?> - TA <?php echo $tahun_ajaran; ?></small>
                </div>
            <?php endif; ?>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="rekapTable">
                    <thead>
                        <tr>
                            <th>No</th>
                            <th>Nama Siswa</th>
                            <th>Kelas</th>
                            <th>Hadir</th>
                            <th>Sakit</th>
                            <th>Izin</th>
                            <th>Alpha</th>
                            <th>Total Pertemuan</th>
                            <th>Persentase Kehadiran</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        while ($row = $rekap_data->fetch(PDO::FETCH_ASSOC)) :
                            $total_hari = $row['total_hadir'] + $row['total_sakit'] + $row['total_izin'] + $row['total_alpha'];
                            $persentase = $total_hari > 0 ? round(($row['total_hadir'] / $total_hari) * 100, 2) : 0;

                            // Set badge color based on percentage
                            if ($persentase >= 90) {
                                $badge_color = 'success';
                            } elseif ($persentase >= 80) {
                                $badge_color = 'info';
                            } elseif ($persentase >= 70) {
                                $badge_color = 'warning';
                            } else {
                                $badge_color = 'danger';
                            }
                        ?>
                        <tr>
                            <td><?php echo $no++; ?></td>
                            <td><?php echo $row['nama_siswa']; ?></td>
                            <td><?php echo $row['nama_kelas']; ?></td>
                            <td><?php echo $row['total_hadir']; ?></td>
                            <td><?php echo $row['total_sakit']; ?></td>
                            <td><?php echo $row['total_izin']; ?></td>
                            <td><?php echo $row['total_alpha']; ?></td>
                            <td><?php echo $total_hari; ?></td>
                            <td>
                                <span class="badge bg-<?php echo $badge_color; ?>">
                                    <?php echo $persentase; ?>%
                                </span>
                            </td>
                            <td>
                                <a href="detail.php?siswa_id=<?php echo $row['siswa_id']; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i> Detail
                                </a>
                            </td>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php elseif ($selected_kelas): ?>
    <div class="alert alert-info">
        Tidak ada data absensi untuk kelas yang dipilih.
    </div>
    <?php else: ?>
    <div class="alert alert-info">
        Silakan pilih kelas untuk melihat rekap absensi.
    </div>
    <?php endif; ?>
</div>

<script>
$(document).ready(function() {
    $('#rekapTable').DataTable({
        "pageLength": 25,
        "order": [[1, 'asc']], // Sort by student name
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
