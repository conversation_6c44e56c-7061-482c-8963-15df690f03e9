<?php
// Start output buffering to prevent "headers already sent" errors
ob_start();

if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

require_once '../template/public_header.php';

// Add custom CSS for report card
echo '<link rel="stylesheet" href="assets/css/rapor-k13.css">';
require_once '../models/Rapor.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/PeriodeAktif.php';

// Check if required parameters are provided
if (!isset($_GET['nis']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    $_SESSION['error'] = "Parameter tidak lengkap.";
    header("Location: index.php");
    exit();
}

// Get parameters
$nis = $_GET['nis'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

// Initialize models
$siswaModel = new Siswa();
$raporModel = new Rapor();

// Get student data
$siswa = $siswaModel->getByNIS($nis);
if (!$siswa) {
    $_SESSION['error'] = "Siswa dengan NIS $nis tidak ditemukan.";
    header("Location: index.php");
    exit();
}

// Get complete report card data
$rapor_data = $raporModel->getCompleteRaporData($siswa['id'], $semester, $tahun_ajaran);

// Format semester display
$semester_text = $semester == '1' ? 'Ganjil' : 'Genap';
?>



<div class="row mb-4 no-print">
    <div class="col-12">
        <a href="student_data.php?nis=<?php echo urlencode($nis); ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo urlencode($tahun_ajaran); ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-2"></i> Kembali
        </a>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print me-2"></i> Cetak
        </button>
        <a href="rapor_pdf.php?nis=<?php echo urlencode($nis); ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo urlencode($tahun_ajaran); ?>" class="btn btn-danger" target="_blank">
            <i class="fas fa-file-pdf me-2"></i> Export PDF
        </a>
    </div>
</div>

<div class="rapor-container">
    <!-- Header Section -->
    <div class="rapor-header">
        <h3><?php echo $rapor_data['school']['nama_sekolah']; ?></h3>
        <p><?php echo $rapor_data['school']['alamat_sekolah']; ?></p>
        <div class="rapor-title">RAPOR PESERTA DIDIK</div>
        <div class="rapor-title">KURIKULUM 2013</div>
    </div>

    <!-- Student Information -->
    <div class="student-info">
        <table>
            <tr>
                <td width="20%">Nama Peserta Didik</td>
                <td width="2%">:</td>
                <td width="28%"><?php echo $rapor_data['siswa']['nama_siswa']; ?></td>
                <td width="20%">Kelas</td>
                <td width="2%">:</td>
                <td width="26%"><?php echo $rapor_data['kelas']['nama_kelas']; ?></td>
            </tr>
            <tr>
                <td>NIS</td>
                <td>:</td>
                <td><?php echo $rapor_data['siswa']['nis']; ?></td>
                <td>Semester</td>
                <td>:</td>
                <td><?php echo $semester_text; ?></td>
            </tr>
            <tr>
                <td>Sekolah</td>
                <td>:</td>
                <td><?php echo $rapor_data['school']['nama_sekolah']; ?></td>
                <td>Tahun Pelajaran</td>
                <td>:</td>
                <td><?php echo $tahun_ajaran; ?></td>
            </tr>
            <tr>
                <td>Alamat</td>
                <td>:</td>
                <td colspan="4"><?php echo $rapor_data['siswa']['alamat'] ?: '-'; ?></td>
            </tr>
        </table>
    </div>

    <!-- Academic Grades Section -->
    <div class="grades-section">
        <h4>A. SIKAP</h4>
        <div class="character-section">
            <table class="character-table">
                <tr>
                    <th width="30%">Aspek yang Dinilai</th>
                    <th width="10%">Nilai</th>
                    <th width="60%">Deskripsi</th>
                </tr>
                <tr>
                    <td>1. Sikap Spiritual</td>
                    <td><?php echo $rapor_data['penilaian_sikap']['sikap_spiritual']; ?></td>
                    <td><?php echo $raporModel->getCharacterDescription($rapor_data['penilaian_sikap']['sikap_spiritual']); ?></td>
                </tr>
                <tr>
                    <td>2. Sikap Sosial</td>
                    <td><?php echo $rapor_data['penilaian_sikap']['sikap_sosial']; ?></td>
                    <td><?php echo $raporModel->getCharacterDescription($rapor_data['penilaian_sikap']['sikap_sosial']); ?></td>
                </tr>
            </table>
        </div>

        <h4>B. PENGETAHUAN DAN KETERAMPILAN</h4>
        <table class="grades-table">
            <thead>
                <tr>
                    <th rowspan="2" width="5%">No</th>
                    <th rowspan="2" width="35%">Mata Pelajaran</th>
                    <th colspan="2" width="20%">Pengetahuan</th>
                    <th colspan="2" width="20%">Keterampilan</th>
                    <th rowspan="2" width="20%">Deskripsi</th>
                </tr>
                <tr>
                    <th>Nilai</th>
                    <th>Predikat</th>
                    <th>Nilai</th>
                    <th>Predikat</th>
                </tr>
            </thead>
            <tbody>
                <?php 
                $no = 1;
                foreach ($rapor_data['nilai_akademik'] as $nilai): 
                    $grade_desc = $raporModel->getGradeDescription($nilai['nilai_akhir'] ?: 0, $nilai['kkm']);
                ?>
                <tr>
                    <td><?php echo $no++; ?></td>
                    <td class="subject-name"><?php echo $nilai['nama_mapel']; ?></td>
                    <td><?php echo $nilai['nilai_akhir'] ?: '-'; ?></td>
                    <td><?php echo $grade_desc['predikat']; ?></td>
                    <td><?php echo $nilai['nilai_akhir'] ?: '-'; ?></td>
                    <td><?php echo $grade_desc['predikat']; ?></td>
                    <td><?php echo $grade_desc['deskripsi']; ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>

    <!-- Attendance Section -->
    <div class="attendance-section">
        <h4>C. KETIDAKHADIRAN</h4>
        <table class="attendance-table">
            <tr>
                <th width="50%">Jenis Ketidakhadiran</th>
                <th width="50%">Jumlah</th>
            </tr>
            <tr>
                <td>Sakit</td>
                <td><?php echo $rapor_data['kehadiran']['sakit']; ?> hari</td>
            </tr>
            <tr>
                <td>Izin</td>
                <td><?php echo $rapor_data['kehadiran']['izin']; ?> hari</td>
            </tr>
            <tr>
                <td>Tanpa Keterangan</td>
                <td><?php echo $rapor_data['kehadiran']['alpha']; ?> hari</td>
            </tr>
        </table>
    </div>

    <!-- Extracurricular Section -->
    <?php if (!empty($rapor_data['ekstrakurikuler'])): ?>
    <div class="extracurricular-section">
        <h4>D. EKSTRAKURIKULER</h4>
        <table class="attendance-table">
            <tr>
                <th width="50%">Kegiatan Ekstrakurikuler</th>
                <th width="25%">Nilai</th>
                <th width="25%">Keterangan</th>
            </tr>
            <?php foreach ($rapor_data['ekstrakurikuler'] as $ekskul): ?>
            <tr>
                <td><?php echo $ekskul['nama_ekstrakurikuler']; ?></td>
                <td><?php echo $ekskul['nilai']; ?></td>
                <td><?php echo $ekskul['keterangan']; ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>
    <?php endif; ?>

    <!-- Achievements Section -->
    <?php if (!empty($rapor_data['prestasi'])): ?>
    <div class="achievement-section">
        <h4>E. PRESTASI</h4>
        <table class="attendance-table">
            <tr>
                <th width="50%">Jenis Prestasi</th>
                <th width="50%">Keterangan</th>
            </tr>
            <?php foreach ($rapor_data['prestasi'] as $prestasi): ?>
            <tr>
                <td><?php echo $prestasi['jenis_prestasi']; ?></td>
                <td><?php echo $prestasi['keterangan']; ?></td>
            </tr>
            <?php endforeach; ?>
        </table>
    </div>
    <?php endif; ?>

    <!-- Notes Section -->
    <div class="notes-section">
        <h4>F. CATATAN WALI KELAS</h4>
        <div style="border: 1px solid #000; padding: 10px; min-height: 60px;">
            <?php echo $rapor_data['catatan_wali_kelas'] ?: 'Tidak ada catatan khusus.'; ?>
        </div>
    </div>

    <!-- Class Promotion Section -->
    <?php if ($semester == '2'): ?>
    <div class="promotion-section">
        <h4>G. KEPUTUSAN</h4>
        <div style="border: 1px solid #000; padding: 10px;">
            Berdasarkan pencapaian kompetensi pada semester ke-1 dan ke-2, peserta didik ditetapkan:
            <br><br>
            <strong><?php echo $rapor_data['status_kenaikan']; ?></strong>
        </div>
    </div>
    <?php endif; ?>

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <p>Mengetahui,<br>Orang Tua/Wali</p>
            <div class="signature-line"></div>
            <p>(...........................)</p>
        </div>

        <div class="signature-box">
            <p><?php echo $rapor_data['school']['kota']; ?>, <?php echo date('d F Y'); ?><br>Wali Kelas</p>
            <div class="signature-line"></div>
            <p>(...........................)</p>
        </div>

        <div class="signature-box">
            <p>Mengetahui,<br>Kepala Sekolah</p>
            <div class="signature-line"></div>
            <p><?php echo $rapor_data['school']['kepala_sekolah']; ?><br>NIP. <?php echo $rapor_data['school']['nip_kepala_sekolah']; ?></p>
        </div>
    </div>
</div>

<?php
require_once '../template/public_footer.php';
?>
