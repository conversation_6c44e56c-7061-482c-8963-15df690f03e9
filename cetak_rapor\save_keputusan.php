<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../models/KenaikanKelas.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../models/Guru.php';
require_once '../models/User.php';

// Check if user is logged in and is a guru or admin
if (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'guru' && $_SESSION['role'] !== 'admin')) {
    header("Location: /absen/403.php");
    exit();
}

// Check if request is POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    $_SESSION['error'] = "Metode request tidak valid.";
    header("Location: /absen/cetak_rapor/index.php");
    exit();
}

// Get POST data
$siswa_id = isset($_POST['siswa_id']) ? $_POST['siswa_id'] : null;
$kelas_id = isset($_POST['kelas_id']) ? $_POST['kelas_id'] : null;
$tahun_ajaran = isset($_POST['tahun_ajaran']) ? $_POST['tahun_ajaran'] : null;
$status = isset($_POST['status']) ? $_POST['status'] : null;
$alasan = isset($_POST['alasan']) ? $_POST['alasan'] : null;
$alasan_lain = isset($_POST['alasan_lain']) ? $_POST['alasan_lain'] : null;

// Validate required fields
if (!$siswa_id || !$kelas_id || !$tahun_ajaran || !$status) {
    $_SESSION['error'] = "Semua field harus diisi.";
    header("Location: /absen/cetak_rapor/index.php?kelas_id=" . $kelas_id . "&tahun_ajaran=" . $tahun_ajaran);
    exit();
}

// Verify access if user is a teacher
if ($_SESSION['role'] === 'guru') {
    $user = new User();
    $guru = new Guru();
    $kelas = new Kelas();
    
    $guru_id = $user->getGuruId($_SESSION['user_id']);
    
    if (!$guru_id) {
        $_SESSION['error'] = "Data guru tidak ditemukan.";
        header("Location: /absen/index.php");
        exit();
    }
    
    // Check if teacher is a homeroom teacher for this class
    $is_wali_kelas = false;
    $kelas_list = $guru->getKelasAsWaliKelas($guru_id);
    
    while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
        if ($row['id'] == $kelas_id) {
            $is_wali_kelas = true;
            break;
        }
    }
    
    if (!$is_wali_kelas) {
        $_SESSION['error'] = "Anda tidak memiliki akses ke kelas ini.";
        header("Location: /absen/index.php");
        exit();
    }
}

// Verify student belongs to the class
$siswa = new Siswa();
$siswa->id = $siswa_id;
if (!$siswa->getOne() || $siswa->kelas_id != $kelas_id) {
    $_SESSION['error'] = "Siswa tidak ditemukan atau bukan bagian dari kelas ini.";
    header("Location: /absen/cetak_rapor/index.php?kelas_id=" . $kelas_id . "&tahun_ajaran=" . $tahun_ajaran);
    exit();
}

// Save keputusan
$kenaikanKelas = new KenaikanKelas();
$kenaikanKelas->siswa_id = $siswa_id;
$kenaikanKelas->tahun_ajaran = $tahun_ajaran;
$kenaikanKelas->status = $status;

// Process alasan
if (is_array($alasan)) {
    $kenaikanKelas->alasan = json_encode($alasan);
} else {
    $kenaikanKelas->alasan = $alasan;
}

$kenaikanKelas->alasan_lain = $alasan_lain;

if ($kenaikanKelas->save()) {
    $_SESSION['success'] = "Keputusan kenaikan kelas berhasil disimpan.";
} else {
    $_SESSION['error'] = "Gagal menyimpan keputusan kenaikan kelas.";
}

// Redirect back
header("Location: /absen/cetak_rapor/index.php?kelas_id=" . $kelas_id . "&tahun_ajaran=" . $tahun_ajaran);
exit();
