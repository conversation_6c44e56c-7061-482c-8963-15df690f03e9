<?php
// First line: turn off output buffering
ob_end_clean();

// Start new output buffering
ob_start();

require_once '../vendor/autoload.php';
require_once '../models/Absensi.php';
require_once '../models/PeriodeAktif.php';
session_start();

use Dompdf\Dompdf;
use Dompdf\Options;

try {
    // Get active period and selected period
    $periode = new PeriodeAktif();
    $periode->getActive();

    // Get semester and tahun ajaran from GET or use active period
    $semester = isset($_GET['semester']) ? $_GET['semester'] : $periode->semester;
    $tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periode->tahun_ajaran;

    // Check if siswa_id is provided
    if (!isset($_GET['siswa_id'])) {
        die("Parameter siswa tidak ditemukan.");
    }

    $absensi = new Absensi();
    $rekap_data = $absensi->getRekapDetailSiswa($_GET['siswa_id'], $semester, $tahun_ajaran);

    // Get first row for student info
    $student_info = $rekap_data->fetch(PDO::FETCH_ASSOC);
    if (!$student_info) {
        die("Data siswa tidak ditemukan.");
    }
    // Reset pointer to beginning
    $rekap_data->execute();

    // Start generating PDF
    $options = new Options();
    $options->set('isHtml5ParserEnabled', true);
    $options->set('isPhpEnabled', true);
    $options->set('defaultFont', 'DejaVu Sans');

    $dompdf = new Dompdf($options);
    $dompdf->setPaper('A4', 'landscape');

    // Generate HTML content
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Detail Absensi Siswa</title>
        <style>
            body {
                font-family: DejaVu Sans, sans-serif;
                font-size: 12px;
                margin: 15px;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
            }
            .student-info {
                margin-bottom: 20px;
            }
            .student-info table {
                width: 50%;
            }
            .student-info td {
                padding: 3px;
                border: none;
            }
            .student-info .label {
                font-weight: bold;
                width: 100px;
            }
            table.data {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
                font-size: 11px;
            }
            table.data th,
            table.data td {
                border: 1px solid #000;
                padding: 5px;
            }
            table.data th {
                background-color: #E2EFDA;
                font-weight: bold;
                text-align: center;
            }
            table.data td {
                text-align: left;
            }
            td.center {
                text-align: center;
            }
            .status-hadir { color: #28a745; }
            .status-alpha { color: #dc3545; }
            .status-sakit { color: #ffc107; }
            .status-izin { color: #17a2b8; }
            .footer {
                margin-top: 30px;
                text-align: right;
                padding-right: 50px;
            }
            .page-break {
                page-break-after: always;
            }
        </style>
    </head>
    <body>
        <div class="header">
            <h3>Detail Absensi Siswa</h3>
        </div>

        <div class="student-info">
            <table>
                <tr>
                    <td class="label">Nama Siswa</td>
                    <td>: ' . htmlspecialchars($student_info['nama_siswa']) . '</td>
                </tr>
                <tr>
                    <td class="label">NIS</td>
                    <td>: ' . htmlspecialchars($student_info['nis']) . '</td>
                </tr>
                <tr>
                    <td class="label">Kelas</td>
                    <td>: ' . htmlspecialchars($student_info['nama_kelas']) . '</td>
                </tr>
            </table>
        </div>

        <table class="data">
            <thead>
                <tr>
                    <th>No</th>
                    <th>Tanggal</th>
                    <th>Mata Pelajaran</th>
                    <th>Guru</th>
                    <th>Status</th>
                    <th>Waktu</th>
                </tr>
            </thead>
            <tbody>';

    $no = 1;
    while ($row = $rekap_data->fetch(PDO::FETCH_ASSOC)) {
        // Determine status class
        $status_class = '';
        switch (strtolower($row['status'])) {
            case 'hadir':
                $status_class = 'status-hadir';
                break;
            case 'sakit':
                $status_class = 'status-sakit';
                break;
            case 'izin':
                $status_class = 'status-izin';
                break;
            case 'alpha':
                $status_class = 'status-alpha';
                break;
        }

        $html .= '<tr>
            <td class="center">' . $no++ . '</td>
            <td class="center">' . date('d/m/Y', strtotime($row['tanggal_absensi'])) . '</td>
            <td>' . htmlspecialchars($row['nama_mapel']) . '</td>
            <td>' . htmlspecialchars($row['nama_guru']) . '</td>
            <td class="center ' . $status_class . '">' . htmlspecialchars($row['status']) . '</td>
            <td class="center">' . htmlspecialchars($row['waktu']) . '</td>
        </tr>';
    }

    $html .= '</tbody>
        </table>

        <div class="footer">
            <p>' . date('d/m/Y') . '</p>
            <p>Wali Kelas</p>
            <br><br><br>
            <p>' . htmlspecialchars($student_info['wali_kelas']) . '</p>
        </div>
    </body>
    </html>';

    $dompdf->loadHtml($html);
    $dompdf->render();

    // Generate file name
    $filename = 'Detail_Absensi_';
    $filename .= preg_replace('/[^a-zA-Z0-9]/', '_', $student_info['nama_siswa']) . '_';
    $filename .= date('Y-m-d_H-i-s') . '.pdf';

    // Clear any previous output
    if (ob_get_length()) ob_end_clean();

    // Set headers
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, no-store, must-revalidate');
    header('Cache-Control: private');
    header('Pragma: no-cache');
    header('Expires: 0');

    echo $dompdf->output();
    exit();

} catch (Exception $e) {
    error_log('PDF Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file PDF. Silakan coba lagi. Error: " . $e->getMessage());
}
