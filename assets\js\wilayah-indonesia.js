/**
 * Wilayah Indonesia API Integration
 * This script handles the integration with the Wilayah Indonesia API
 * to populate dropdown menus for provinces, regencies, districts, and villages.
 */

// Try both API endpoints in case one is not working
const API_URL = 'https://api-alamat-wilayah-indonesia-2024.vercel.app';
const BACKUP_API_URL = 'https://www.emsifa.com/api-wilayah-indonesia/api';

// Function to fetch data from the API
async function fetchData(endpoint) {
    // Try primary API first
    try {
        console.log(`Fetching data from: ${endpoint}`);

        // Add CORS mode and headers
        const response = await fetch(endpoint, {
            method: 'GET',
            mode: 'cors',
            headers: {
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const data = await response.json();
        console.log(`Data received:`, data);
        return data;
    } catch (primaryError) {
        console.error('Error fetching data from primary API:', primaryError);

        // If primary API fails, try backup API
        if (endpoint.includes(API_URL)) {
            const backupEndpoint = endpoint.replace(API_URL, BACKUP_API_URL);
            console.log(`Trying backup API: ${backupEndpoint}`);

            try {
                const backupResponse = await fetch(backupEndpoint, {
                    method: 'GET',
                    mode: 'cors',
                    headers: {
                        'Accept': 'application/json'
                    }
                });

                if (!backupResponse.ok) {
                    throw new Error(`HTTP error! Status: ${backupResponse.status}`);
                }

                const backupData = await backupResponse.json();
                console.log(`Data received from backup API:`, backupData);

                // Transform data to match primary API format if needed
                if (endpoint.includes('/provinsi')) {
                    return backupData.map(p => ({ kode_provinsi: p.id, nama_provinsi: p.name }));
                } else if (endpoint.includes('/kabupaten')) {
                    const provinceId = endpoint.split('=')[1];
                    return backupData.map(k => ({
                        kode_kabupaten: k.id,
                        nama_kabupaten: k.name,
                        kode_provinsi: provinceId
                    }));
                } else if (endpoint.includes('/kecamatan')) {
                    const regencyId = endpoint.split('=')[1];
                    return backupData.map(k => ({
                        kode_kecamatan: k.id,
                        nama_kecamatan: k.name,
                        kode_kabupaten: regencyId
                    }));
                } else if (endpoint.includes('/desa')) {
                    const districtId = endpoint.split('=')[1];
                    return backupData.map(d => ({
                        kode_desa: d.id,
                        nama_desa: d.name,
                        kode_kecamatan: districtId
                    }));
                }

                return backupData;
            } catch (backupError) {
                console.error('Error fetching data from backup API:', backupError);
                console.error('Both APIs failed. Please try again later.');
                return [];
            }
        }

        return [];
    }
}

// Function to populate a dropdown with options
function populateDropdown(selectElement, data, valueField, textField, defaultText = '-- Pilih --') {
    // Clear existing options
    selectElement.innerHTML = '';

    // Add default option
    const defaultOption = document.createElement('option');
    defaultOption.value = '';
    defaultOption.textContent = defaultText;
    selectElement.appendChild(defaultOption);

    // Add options from data
    data.forEach(item => {
        const option = document.createElement('option');
        option.value = item[valueField];
        option.textContent = item[textField];
        selectElement.appendChild(option);
    });
}

// Function to initialize province dropdown
async function initProvinces(provinsiSelect, selectedProvinsi = '') {
    console.log('Initializing provinces dropdown');

    // Show loading indicator
    provinsiSelect.innerHTML = '<option value="">Loading provinces...</option>';

    try {
        // Try to fetch provinces from the API
        const provinces = await fetchData(`${API_URL}/provinsi`);

        if (provinces && provinces.length > 0) {
            console.log(`Loaded ${provinces.length} provinces`);
            populateDropdown(provinsiSelect, provinces, 'kode_provinsi', 'nama_provinsi', '-- Pilih Provinsi --');

            if (selectedProvinsi) {
                provinsiSelect.value = selectedProvinsi;
                provinsiSelect.dispatchEvent(new Event('change'));
            }
        } else {
            // If no provinces were loaded, try a direct fetch from the backup API
            console.log('Trying direct fetch from backup API');
            const backupProvinces = await fetch(`${BACKUP_API_URL}/provinces.json`)
                .then(response => response.json())
                .then(data => data.map(p => ({ kode_provinsi: p.id, nama_provinsi: p.name })))
                .catch(error => {
                    console.error('Direct backup API fetch failed:', error);
                    return [];
                });

            if (backupProvinces && backupProvinces.length > 0) {
                console.log(`Loaded ${backupProvinces.length} provinces from direct backup API`);
                populateDropdown(provinsiSelect, backupProvinces, 'kode_provinsi', 'nama_provinsi', '-- Pilih Provinsi --');

                if (selectedProvinsi) {
                    provinsiSelect.value = selectedProvinsi;
                    provinsiSelect.dispatchEvent(new Event('change'));
                }
            } else {
                console.error('No provinces data received from any API');
                provinsiSelect.innerHTML = '<option value="">Error loading provinces</option>';
            }
        }
    } catch (error) {
        console.error('Error in initProvinces:', error);
        provinsiSelect.innerHTML = '<option value="">Error loading provinces</option>';
    }
}

// Function to load kabupaten/kota based on selected province
async function loadKabupaten(provinsiSelect, kabupatenSelect, selectedKabupaten = '') {
    const kodeProvinsi = provinsiSelect.value;
    if (!kodeProvinsi) {
        // Clear kabupaten dropdown if no province is selected
        kabupatenSelect.innerHTML = '<option value="">-- Pilih Kabupaten/Kota --</option>';
        return;
    }

    // Show loading indicator
    kabupatenSelect.innerHTML = '<option value="">Loading kabupaten/kota...</option>';

    try {
        // Try primary API
        const kabupaten = await fetchData(`${API_URL}/kabupaten?kode_provinsi=${kodeProvinsi}`);

        if (kabupaten && kabupaten.length > 0) {
            populateDropdown(kabupatenSelect, kabupaten, 'kode_kabupaten', 'nama_kabupaten', '-- Pilih Kabupaten/Kota --');

            if (selectedKabupaten) {
                kabupatenSelect.value = selectedKabupaten;
                kabupatenSelect.dispatchEvent(new Event('change'));
            }
        } else {
            // Try backup API directly
            console.log('Trying direct fetch from backup API for regencies');
            const backupKabupaten = await fetch(`${BACKUP_API_URL}/regencies/${kodeProvinsi}.json`)
                .then(response => response.json())
                .then(data => data.map(k => ({
                    kode_kabupaten: k.id,
                    nama_kabupaten: k.name,
                    kode_provinsi: kodeProvinsi
                })))
                .catch(error => {
                    console.error('Direct backup API fetch for regencies failed:', error);
                    return [];
                });

            if (backupKabupaten && backupKabupaten.length > 0) {
                populateDropdown(kabupatenSelect, backupKabupaten, 'kode_kabupaten', 'nama_kabupaten', '-- Pilih Kabupaten/Kota --');

                if (selectedKabupaten) {
                    kabupatenSelect.value = selectedKabupaten;
                    kabupatenSelect.dispatchEvent(new Event('change'));
                }
            } else {
                console.error('No kabupaten data received from any API');
                kabupatenSelect.innerHTML = '<option value="">Error loading kabupaten/kota</option>';
            }
        }
    } catch (error) {
        console.error('Error in loadKabupaten:', error);
        kabupatenSelect.innerHTML = '<option value="">Error loading kabupaten/kota</option>';
    }
}

// Function to load kecamatan based on selected kabupaten/kota
async function loadKecamatan(kabupatenSelect, kecamatanSelect, selectedKecamatan = '') {
    const kodeKabupaten = kabupatenSelect.value;
    if (!kodeKabupaten) {
        // Clear kecamatan dropdown if no kabupaten is selected
        kecamatanSelect.innerHTML = '<option value="">-- Pilih Kecamatan --</option>';
        return;
    }

    // Show loading indicator
    kecamatanSelect.innerHTML = '<option value="">Loading kecamatan...</option>';

    try {
        // Try primary API
        const kecamatan = await fetchData(`${API_URL}/kecamatan?kode_kabupaten=${kodeKabupaten}`);

        if (kecamatan && kecamatan.length > 0) {
            populateDropdown(kecamatanSelect, kecamatan, 'kode_kecamatan', 'nama_kecamatan', '-- Pilih Kecamatan --');

            if (selectedKecamatan) {
                kecamatanSelect.value = selectedKecamatan;
                kecamatanSelect.dispatchEvent(new Event('change'));
            }
        } else {
            // Try backup API directly
            console.log('Trying direct fetch from backup API for districts');
            const backupKecamatan = await fetch(`${BACKUP_API_URL}/districts/${kodeKabupaten}.json`)
                .then(response => response.json())
                .then(data => data.map(k => ({
                    kode_kecamatan: k.id,
                    nama_kecamatan: k.name,
                    kode_kabupaten: kodeKabupaten
                })))
                .catch(error => {
                    console.error('Direct backup API fetch for districts failed:', error);
                    return [];
                });

            if (backupKecamatan && backupKecamatan.length > 0) {
                populateDropdown(kecamatanSelect, backupKecamatan, 'kode_kecamatan', 'nama_kecamatan', '-- Pilih Kecamatan --');

                if (selectedKecamatan) {
                    kecamatanSelect.value = selectedKecamatan;
                    kecamatanSelect.dispatchEvent(new Event('change'));
                }
            } else {
                console.error('No kecamatan data received from any API');
                kecamatanSelect.innerHTML = '<option value="">Error loading kecamatan</option>';
            }
        }
    } catch (error) {
        console.error('Error in loadKecamatan:', error);
        kecamatanSelect.innerHTML = '<option value="">Error loading kecamatan</option>';
    }
}

// Function to load desa/kelurahan based on selected kecamatan
async function loadDesa(kecamatanSelect, desaSelect, selectedDesa = '') {
    const kodeKecamatan = kecamatanSelect.value;
    if (!kodeKecamatan) {
        // Clear desa dropdown if no kecamatan is selected
        desaSelect.innerHTML = '<option value="">-- Pilih Desa/Kelurahan --</option>';
        return;
    }

    // Show loading indicator
    desaSelect.innerHTML = '<option value="">Loading desa/kelurahan...</option>';

    try {
        // Try primary API
        const desa = await fetchData(`${API_URL}/desa?kode_kecamatan=${kodeKecamatan}`);

        if (desa && desa.length > 0) {
            populateDropdown(desaSelect, desa, 'kode_desa', 'nama_desa', '-- Pilih Desa/Kelurahan --');

            if (selectedDesa) {
                desaSelect.value = selectedDesa;
                desaSelect.dispatchEvent(new Event('change'));
            }
        } else {
            // Try backup API directly
            console.log('Trying direct fetch from backup API for villages');
            const backupDesa = await fetch(`${BACKUP_API_URL}/villages/${kodeKecamatan}.json`)
                .then(response => response.json())
                .then(data => data.map(d => ({
                    kode_desa: d.id,
                    nama_desa: d.name,
                    kode_kecamatan: kodeKecamatan
                })))
                .catch(error => {
                    console.error('Direct backup API fetch for villages failed:', error);
                    return [];
                });

            if (backupDesa && backupDesa.length > 0) {
                populateDropdown(desaSelect, backupDesa, 'kode_desa', 'nama_desa', '-- Pilih Desa/Kelurahan --');

                if (selectedDesa) {
                    desaSelect.value = selectedDesa;
                    desaSelect.dispatchEvent(new Event('change'));
                }
            } else {
                console.error('No desa data received from any API');
                desaSelect.innerHTML = '<option value="">Error loading desa/kelurahan</option>';
            }
        }
    } catch (error) {
        console.error('Error in loadDesa:', error);
        desaSelect.innerHTML = '<option value="">Error loading desa/kelurahan</option>';
    }
}

// Function to load kode pos based on selected desa/kelurahan
async function loadKodePos(desaSelect, kodePosInput) {
    const kodeDesa = desaSelect.value;
    if (!kodeDesa) {
        return;
    }

    try {
        // Try primary API
        console.log(`Fetching kode pos for desa: ${kodeDesa}`);
        const kodePos = await fetchData(`${API_URL}/kodepos?kode_desa=${kodeDesa}`);

        if (kodePos && kodePos.length > 0 && kodePos[0].kode_pos) {
            console.log(`Kode pos found: ${kodePos[0].kode_pos}`);
            kodePosInput.value = kodePos[0].kode_pos;
        } else {
            console.log('No kode pos found in primary API');

            // Try backup method - we don't have a direct API for kode pos in the backup API
            // Instead, we'll try to extract it from the desa name if it contains a postal code pattern
            const desaName = desaSelect.options[desaSelect.selectedIndex].textContent;
            const postalCodeMatch = desaName.match(/\b\d{5}\b/); // Look for 5-digit number

            if (postalCodeMatch) {
                console.log(`Extracted kode pos from desa name: ${postalCodeMatch[0]}`);
                kodePosInput.value = postalCodeMatch[0];
            } else {
                console.log('No kode pos found in desa name');
                // Keep existing value if it exists
                if (!kodePosInput.value) {
                    console.log('No existing kode pos value, showing placeholder');
                    kodePosInput.placeholder = "Masukkan kode pos secara manual";
                }
            }
        }
    } catch (error) {
        console.error('Error fetching kode pos:', error);
        // Keep existing value if it exists
        if (!kodePosInput.value) {
            kodePosInput.placeholder = "Masukkan kode pos secara manual";
        }
    }
}

// Function to initialize all dropdowns
function initWilayahDropdowns(
    provinsiSelect,
    kabupatenSelect,
    kecamatanSelect,
    desaSelect,
    kodePosInput,
    selectedProvinsi = '',
    selectedKabupaten = '',
    selectedKecamatan = '',
    selectedDesa = ''
) {
    // Initialize province dropdown
    initProvinces(provinsiSelect, selectedProvinsi);

    // Set up event listeners
    provinsiSelect.addEventListener('change', () => {
        loadKabupaten(provinsiSelect, kabupatenSelect);
        // Clear dependent dropdowns
        kecamatanSelect.innerHTML = '<option value="">-- Pilih Kecamatan --</option>';
        desaSelect.innerHTML = '<option value="">-- Pilih Desa/Kelurahan --</option>';
    });

    kabupatenSelect.addEventListener('change', () => {
        loadKecamatan(kabupatenSelect, kecamatanSelect);
        // Clear dependent dropdowns
        desaSelect.innerHTML = '<option value="">-- Pilih Desa/Kelurahan --</option>';
    });

    kecamatanSelect.addEventListener('change', () => {
        loadDesa(kecamatanSelect, desaSelect);
    });

    desaSelect.addEventListener('change', () => {
        loadKodePos(desaSelect, kodePosInput);
    });

    // If we have pre-selected values, load the data
    if (selectedProvinsi) {
        loadKabupaten(provinsiSelect, kabupatenSelect, selectedKabupaten);
    }

    if (selectedKabupaten) {
        loadKecamatan(kabupatenSelect, kecamatanSelect, selectedKecamatan);
    }

    if (selectedKecamatan) {
        loadDesa(kecamatanSelect, desaSelect, selectedDesa);
    }
}

// Export functions for use in other scripts
window.wilayahIndonesia = {
    initWilayahDropdowns,
    initProvinces,
    loadKabupaten,
    loadKecamatan,
    loadDesa,
    loadKodePos
};
