<?php
require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Guru.php';
require_once '../models/User.php';
require_once '../models/Nilai.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Tugas.php';
require_once '../models/NilaiTugas.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

session_start();

// Check if user is logged in and is a guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: /absen/403.php");
    exit();
}

// Get guru_id from user_id
$user = new User();
$guru_id = $user->getGuruId($_SESSION['user_id']);

if (!$guru_id) {
    $_SESSION['error'] = "Data guru tidak ditemukan.";
    header("Location: /absen/index.php");
    exit();
}

// Check if guru is wali kelas
$guru = new Guru();
if (!$guru->isWaliKelas($guru_id)) {
    header("Location: /absen/403.php");
    exit();
}

// Get active period and selected period
$periode = new PeriodeAktif();
$periode->getActive();

// Get semester and tahun ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : $periode->semester;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periode->tahun_ajaran;

$nilai = new Nilai();
$mapel = new MataPelajaran();
$tugas = new Tugas();
$nilaiTugas = new NilaiTugas();
$kelas = new Kelas();

// Get selected class and verify it belongs to this wali kelas
$selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
if (!$selected_kelas) {
    die("Kelas tidak dipilih.");
}

$valid_kelas = false;
$kelas_list = $guru->getKelasAsWaliKelas($guru_id);
while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
    if ($row['id'] == $selected_kelas) {
        $valid_kelas = true;
        $nama_kelas = $row['nama_kelas'];
        break;
    }
}
if (!$valid_kelas) {
    header("Location: /absen/403.php");
    exit();
}

// Get selected mapel
$selected_mapel = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : 'all';

try {
    // Create new Spreadsheet object
    $spreadsheet = new Spreadsheet();
    
    // Get all mapel for this class
    $mapel_list = $mapel->getByKelas($selected_kelas);
    
    // Get student list once
    $siswa_list = $nilai->getSiswaByKelas($selected_kelas);
    $siswa_data = [];
    while ($s = $siswa_list->fetch(PDO::FETCH_ASSOC)) {
        $siswa_data[] = $s;
    }

    // Counter for sheets
    $sheet_index = 0;
    
    // Create a sheet for each mapel
    while ($m = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
        // Skip if not selected
        if ($selected_mapel != 'all' && $selected_mapel != $m['id']) {
            continue;
        }
        
        if ($sheet_index > 0) {
            $spreadsheet->createSheet();
        }
        $sheet = $spreadsheet->setActiveSheetIndex($sheet_index);
        
        // Set sheet title (max 31 chars)
        $sheet_title = substr($m['kode_mapel'], 0, 31);
        $sheet->setTitle($sheet_title);
        
        // Set column widths
        $sheet->getColumnDimension('A')->setWidth(5);  // No
        $sheet->getColumnDimension('B')->setWidth(15); // NIS
        $sheet->getColumnDimension('C')->setWidth(30); // Nama
        $sheet->getColumnDimension('D')->setWidth(15); // Rata-rata Tugas
        $sheet->getColumnDimension('E')->setWidth(10); // UTS
        $sheet->getColumnDimension('F')->setWidth(10); // UAS
        $sheet->getColumnDimension('G')->setWidth(10); // Absensi
        $sheet->getColumnDimension('H')->setWidth(15); // Rumus
        $sheet->getColumnDimension('I')->setWidth(12); // Nilai Akhir
        $sheet->getColumnDimension('J')->setWidth(10); // Status

        // Header
        $sheet->mergeCells('A1:J1');
        $sheet->setCellValue('A1', 'REKAP NILAI');
        $sheet->mergeCells('A2:J2');
        $sheet->setCellValue('A2', 'Kelas: ' . $nama_kelas);
        $sheet->mergeCells('A3:J3');
        $sheet->setCellValue('A3', 'Mata Pelajaran: ' . $m['nama_mapel']);
        $sheet->mergeCells('A4:J4');
        $sheet->setCellValue('A4', 'Semester: ' . $semester . ' - Tahun Ajaran: ' . $tahun_ajaran);

        // Style the header
        $headerStyle = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ];
        $sheet->getStyle('A1:J4')->applyFromArray($headerStyle);

        // Table headers
        $headers = ['No', 'NIS', 'Nama Siswa', 'Rata-rata Tugas', 'UTS', 'UAS', 'Absensi', 'Rumus', 'Nilai Akhir', 'Status'];
        $sheet->fromArray($headers, NULL, 'A6');

        // Style the table headers
        $tableHeaderStyle = [
            'font' => [
                'bold' => true,
            ],
            'alignment' => [
                'horizontal' => Alignment::HORIZONTAL_CENTER,
            ],
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => [
                    'rgb' => 'E0E0E0',
                ],
            ],
        ];
        $sheet->getStyle('A6:J6')->applyFromArray($tableHeaderStyle);

        // Data rows
        $row = 7;
        $no = 1;
        foreach ($siswa_data as $siswa) {
            $nilai_data = $nilai->getNilaiSiswa($siswa['id'], $m['id'], $semester, $tahun_ajaran);
            $rata_tugas = $nilaiTugas->getRataTugas($siswa['id'], $m['id'], $semester, $tahun_ajaran);
            $nilai_akhir = $nilai_data['nilai_akhir'] ?? 0;
            $status = $nilai_akhir >= $m['kkm'] ? 'Tuntas' : 'Belum Tuntas';

            $sheet->setCellValue('A'.$row, $no++);
            $sheet->setCellValue('B'.$row, $siswa['nis']);
            $sheet->setCellValue('C'.$row, $siswa['nama_siswa']);
            $sheet->setCellValue('D'.$row, $rata_tugas ? number_format($rata_tugas, 2) : '-');
            $sheet->setCellValue('E'.$row, $nilai_data['nilai_uts'] ?? '-');
            $sheet->setCellValue('F'.$row, $nilai_data['nilai_uas'] ?? '-');
            $sheet->setCellValue('G'.$row, $nilai_data['nilai_absen'] ?? '-');
            $sheet->setCellValue('H'.$row, $nilai_data['rumus_nilai'] ?? '-');
            $sheet->setCellValue('I'.$row, $nilai_akhir ?: '-');
            $sheet->setCellValue('J'.$row, $status);

            // Add conditional formatting for nilai akhir and status
            if ($nilai_akhir > 0) {
                $color = $nilai_akhir >= $m['kkm'] ? '00FF00' : 'FF0000';
                $sheet->getStyle('I'.$row)->getFont()->getColor()->setRGB($color);
                $sheet->getStyle('J'.$row)->getFont()->getColor()->setRGB($color);
            }

            $row++;
        }

        // Style the data
        $dataStyle = [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_THIN,
                ],
            ],
            'alignment' => [
                'vertical' => Alignment::VERTICAL_CENTER,
            ],
        ];
        $sheet->getStyle('A7:J'.($row-1))->applyFromArray($dataStyle);

        // Center numeric columns
        $sheet->getStyle('A7:A'.($row-1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('D7:J'.($row-1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

        $sheet_index++;
    }

    // Set the first sheet as active
    $spreadsheet->setActiveSheetIndex(0);

    // Create Excel file
    $writer = new Xlsx($spreadsheet);

    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="Rekap_Nilai_'.$nama_kelas.'_'.$semester.'_'.$tahun_ajaran.'.xlsx"');
    header('Cache-Control: max-age=0');

    // Output file
    $writer->save('php://output');
    exit;

} catch (Exception $e) {
    error_log('Excel Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file Excel. Silakan coba lagi.");
}
