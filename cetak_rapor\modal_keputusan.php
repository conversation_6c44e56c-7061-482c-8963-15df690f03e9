<?php
require_once __DIR__ . '/../models/KenaikanKelas.php';
require_once __DIR__ . '/../models/Siswa.php';
require_once __DIR__ . '/../models/Rapor.php';

// Get parameters
$siswa_id = isset($_GET['siswa_id']) ? $_GET['siswa_id'] : null;
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : null;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : null;
$semester = isset($_GET['semester']) ? $_GET['semester'] : '2'; // Default to semester 2

if (!$siswa_id || !$kelas_id || !$tahun_ajaran) {
    echo json_encode(['error' => 'Parameter tidak lengkap']);
    exit();
}

// Get student data
$siswa = new Siswa();
$siswa->id = $siswa_id;
if (!$siswa->getOne()) {
    echo json_encode(['error' => 'Siswa tidak ditemukan']);
    exit();
}

// Get existing keputusan
$kenaikanKelas = new KenaikanKelas();
$keputusan = $kenaikanKelas->getByStudent($siswa_id, $tahun_ajaran);

// Get automatic recommendation
$rapor = new Rapor();
$rekomendasi = $rapor->getStatusKenaikanKelas($siswa_id, $semester, $tahun_ajaran);

// Prepare alasan array
$alasan_array = [];
if ($keputusan && isset($keputusan['alasan']) && !empty($keputusan['alasan'])) {
    $alasan_json = $keputusan['alasan'];
    if (json_decode($alasan_json) !== null) {
        $alasan_array = json_decode($alasan_json, true);
    } else {
        $alasan_array = [$alasan_json];
    }
}

// Determine status
$status = $keputusan ? $keputusan['status'] : (strpos($rekomendasi, 'Tinggal Kelas') !== false ? 'Tinggal Kelas' : 'Naik Kelas');
?>

<div class="modal-header">
    <h5 class="modal-title">Edit Keputusan Kenaikan Kelas</h5>
    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
</div>
<div class="modal-body">
    <form id="formKeputusan" action="save_keputusan.php" method="POST">
        <input type="hidden" name="siswa_id" value="<?php echo $siswa_id; ?>">
        <input type="hidden" name="kelas_id" value="<?php echo $kelas_id; ?>">
        <input type="hidden" name="tahun_ajaran" value="<?php echo $tahun_ajaran; ?>">

        <div class="form-group">
            <label>Nama Siswa</label>
            <input type="text" class="form-control" value="<?php echo $siswa->nama_siswa; ?>" readonly>
        </div>

        <div class="form-group">
            <label>NIS</label>
            <input type="text" class="form-control" value="<?php echo $siswa->nis; ?>" readonly>
        </div>

        <div class="form-group">
            <label>Rekomendasi Sistem</label>
            <input type="text" class="form-control" value="<?php echo $rekomendasi; ?>" readonly>
        </div>

        <div class="form-group">
            <label>Status Kenaikan Kelas</label>
            <div class="form-check">
                <input class="form-check-input" type="radio" name="status" id="statusNaik" value="Naik Kelas" <?php echo ($status == 'Naik Kelas') ? 'checked' : ''; ?>>
                <label class="form-check-label" for="statusNaik">
                    Naik Kelas
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="radio" name="status" id="statusNaikBersyarat" value="Naik Kelas Bersyarat" <?php echo ($status == 'Naik Kelas Bersyarat') ? 'checked' : ''; ?>>
                <label class="form-check-label" for="statusNaikBersyarat">
                    Naik Kelas Bersyarat
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="radio" name="status" id="statusTinggal" value="Tinggal Kelas" <?php echo ($status == 'Tinggal Kelas') ? 'checked' : ''; ?>>
                <label class="form-check-label" for="statusTinggal">
                    Tinggal Kelas
                </label>
            </div>
        </div>

        <div id="alasanTinggalContainer" class="form-group" <?php echo ($status != 'Tinggal Kelas') ? 'style="display:none;"' : ''; ?>>
            <label>Alasan Tinggal Kelas</label>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanNilai" value="Nilai tidak mencapai KKM" <?php echo (in_array('Nilai tidak mencapai KKM', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanNilai">
                    Nilai tidak mencapai KKM
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanMapelInti" value="Gagal mata pelajaran inti" <?php echo (in_array('Gagal mata pelajaran inti', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanMapelInti">
                    Gagal mata pelajaran inti
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanKehadiran" value="Kehadiran kurang" <?php echo (in_array('Kehadiran kurang', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanKehadiran">
                    Kehadiran kurang
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanSikap" value="Sikap kurang baik" <?php echo (in_array('Sikap kurang baik', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanSikap">
                    Sikap kurang baik
                </label>
            </div>
        </div>

        <div id="alasanBersyaratContainer" class="form-group" <?php echo ($status != 'Naik Kelas Bersyarat') ? 'style="display:none;"' : ''; ?>>
            <label>Alasan Naik Kelas Bersyarat</label>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanPerbaikan" value="Harus mengikuti perbaikan nilai" <?php echo (in_array('Harus mengikuti perbaikan nilai', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanPerbaikan">
                    Harus mengikuti perbaikan nilai
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanTugas" value="Harus menyelesaikan tugas tertinggal" <?php echo (in_array('Harus menyelesaikan tugas tertinggal', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanTugas">
                    Harus menyelesaikan tugas tertinggal
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanPerilaku" value="Harus memperbaiki perilaku" <?php echo (in_array('Harus memperbaiki perilaku', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanPerilaku">
                    Harus memperbaiki perilaku
                </label>
            </div>
            <div class="form-check">
                <input class="form-check-input" type="checkbox" name="alasan[]" id="alasanPengawasan" value="Perlu pengawasan khusus" <?php echo (in_array('Perlu pengawasan khusus', $alasan_array)) ? 'checked' : ''; ?>>
                <label class="form-check-label" for="alasanPengawasan">
                    Perlu pengawasan khusus
                </label>
            </div>
        </div>

        <div class="form-group">
            <label>Catatan Tambahan</label>
            <textarea class="form-control" name="alasan_lain" rows="3"><?php echo ($keputusan && isset($keputusan['alasan_lain'])) ? $keputusan['alasan_lain'] : ''; ?></textarea>
        </div>
    </form>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
    <button type="button" class="btn btn-primary" onclick="document.getElementById('formKeputusan').submit();">Simpan</button>
</div>

<script>
    $(document).ready(function() {
        $('input[name="status"]').change(function() {
            // Hide all alasan containers first
            $('#alasanTinggalContainer, #alasanBersyaratContainer').hide();

            // Show the appropriate container based on selected status
            if ($(this).val() === 'Tinggal Kelas') {
                $('#alasanTinggalContainer').show();
            } else if ($(this).val() === 'Naik Kelas Bersyarat') {
                $('#alasanBersyaratContainer').show();
            }
        });
    });
</script>
