<?php
require_once '../template/header.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Guru.php';
require_once '../models/User.php';
require_once '../models/Nilai.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Tugas.php';
require_once '../models/NilaiTugas.php';

// Check if user is logged in and is a guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: /absen/403.php");
    exit();
}

// Get guru_id from user_id
$user = new User();
$guru_id = $user->getGuruId($_SESSION['user_id']);

if (!$guru_id) {
    $_SESSION['error'] = "Data guru tidak ditemukan.";
    header("Location: /absen/index.php");
    exit();
}

// Check if guru is wali kelas
$guru = new Guru();
if (!$guru->isWaliKelas($guru_id)) {
    header("Location: /absen/403.php");
    exit();
}

// Get active period
$periode = new PeriodeAktif();
if (!$periode->getActive()) {
    echo "<div class='alert alert-warning'>Tidak ada periode aktif saat ini. Silahkan set periode aktif terlebih dahulu.</div>";
    require_once '../template/footer.php';
    exit;
}

$nilai = new Nilai();
$mapel = new MataPelajaran();
$tugas = new Tugas();
$nilaiTugas = new NilaiTugas();

// Get class list - only show classes where the guru is wali kelas
$kelas_list = $guru->getKelasAsWaliKelas($guru_id);

// Get selected class - verify it belongs to this wali kelas
$selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$selected_mapel = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : 'all';

if ($selected_kelas) {
    $valid_kelas = false;
    $kelas_list_check = $guru->getKelasAsWaliKelas($guru_id);
    while ($row = $kelas_list_check->fetch(PDO::FETCH_ASSOC)) {
        if ($row['id'] == $selected_kelas) {
            $valid_kelas = true;
            break;
        }
    }
    if (!$valid_kelas) {
        header("Location: /absen/403.php");
        exit();
    }
}
?>

<div class="container-fluid">
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">Filter Data</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-4">
                    <label for="kelas_id" class="form-label">Kelas</label>
                    <select name="kelas_id" id="kelas_id" class="form-select" required>
                        <option value="">Pilih Kelas</option>
                        <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) : ?>
                            <option value="<?php echo $row['id']; ?>" <?php echo $selected_kelas == $row['id'] ? 'selected' : ''; ?>>
                                <?php echo $row['nama_kelas']; ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <?php if ($selected_kelas): ?>
                <div class="col-md-4">
                    <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                    <select name="mapel_id" id="mapel_id" class="form-select">
                        <option value="all">Semua Mata Pelajaran</option>
                        <?php 
                        $mapel_list = $mapel->getByKelas($selected_kelas);
                        while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) : 
                        ?>
                            <option value="<?php echo $row['id']; ?>" <?php echo $selected_mapel == $row['id'] ? 'selected' : ''; ?>>
                                <?php echo $row['nama_mapel']; ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <?php endif; ?>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <button type="submit" class="btn btn-primary d-block">Tampilkan</button>
                </div>
            </form>
        </div>
    </div>

    <?php if ($selected_kelas): ?>
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">Rekap Nilai Periode <?php echo $periode->semester . ' - ' . $periode->tahun_ajaran; ?></h5>
            <div>
                <button class="btn btn-success btn-sm" onclick="exportToExcel()">
                    <i class="fas fa-file-excel"></i> Export Excel
                </button>
                <button class="btn btn-danger btn-sm" onclick="exportToPDF()">
                    <i class="fas fa-file-pdf"></i> Export PDF
                </button>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover" id="rekapNilaiTable">
                    <thead>
                        <tr>
                            <th rowspan="2">No</th>
                            <th rowspan="2">NIS</th>
                            <th rowspan="2">Nama Siswa</th>
                            <?php
                            // Get subjects based on filter
                            $mapel_list = $mapel->getByKelas($selected_kelas);
                            $mapel_data = [];
                            while ($m = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
                                if ($selected_mapel == 'all' || $selected_mapel == $m['id']) {
                                    $mapel_data[] = $m;
                                    echo '<th colspan="7" class="text-center">'.$m['nama_mapel'].' (KKM: '.$m['kkm'].')</th>';
                                }
                            }
                            ?>
                        </tr>
                        <tr>
                            <?php foreach ($mapel_data as $m): ?>
                            <th>Rata-rata Tugas</th>
                            <th>UTS</th>
                            <th>UAS</th>
                            <th>Absensi</th>
                            <th>Rumus</th>
                            <th>Nilai Akhir</th>
                            <th>Status</th>
                            <?php endforeach; ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $no = 1;
                        $siswa_list = $nilai->getSiswaByKelas($selected_kelas, $periode->tahun_ajaran, $periode->semester);
                        while ($siswa = $siswa_list->fetch(PDO::FETCH_ASSOC)):
                            $siswa_id = $siswa['id'];
                        ?>
                        <tr>
                            <td><?php echo $no++; ?></td>
                            <td><?php echo $siswa['nis']; ?></td>
                            <td><?php echo $siswa['nama_siswa']; ?></td>
                            <?php foreach ($mapel_data as $m):
                                $nilai_data = $nilai->getNilaiSiswa($siswa_id, $m['id'], $periode->semester, $periode->tahun_ajaran);
                                $rata_tugas = $nilaiTugas->getRataTugas($siswa_id, $m['id'], $periode->semester, $periode->tahun_ajaran);
                                $nilai_akhir = $nilai_data['nilai_akhir'] ?? 0;
                                $status = $nilai_akhir >= $m['kkm'] ? 'Tuntas' : 'Belum Tuntas';
                                $color_class = $nilai_akhir >= $m['kkm'] ? 'text-success' : 'text-danger';
                            ?>
                            <td><?php echo $rata_tugas ? number_format($rata_tugas, 2) : '-'; ?></td>
                            <td><?php echo $nilai_data['nilai_uts'] ?? '-'; ?></td>
                            <td><?php echo $nilai_data['nilai_uas'] ?? '-'; ?></td>
                            <td><?php echo $nilai_data['nilai_absen'] ?? '-'; ?></td>
                            <td><?php echo $nilai_data['rumus_nilai'] ?? '-'; ?></td>
                            <td class="<?php echo $color_class; ?> fw-bold"><?php echo $nilai_akhir ?: '-'; ?></td>
                            <td class="<?php echo $color_class; ?>"><?php echo $status; ?></td>
                            <?php endforeach; ?>
                        </tr>
                        <?php endwhile; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
$(document).ready(function() {
    $('#rekapNilaiTable').DataTable({
        "pageLength": -1,  // Show all entries by default
        "lengthMenu": [[-1, 10, 25, 50, 100], ["All", 10, 25, 50, 100]],  // Put "All" as first option
        "scrollX": true,
        "order": [[2, 'asc']], // Sort by student name
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.20/i18n/Indonesian.json"
        }
    });
});

function exportToExcel() {
    window.location.href = 'export_excel.php?kelas_id=' + $('#kelas_id').val() + '&mapel_id=' + $('#mapel_id').val();
}

function exportToPDF() {
    window.location.href = 'export_pdf.php?kelas_id=' + $('#kelas_id').val() + '&mapel_id=' + $('#mapel_id').val();
}
</script>

<?php
require_once '../template/footer.php';
?>
