<?php
require_once '../template/header.php';
require_once '../models/Siswa.php';
require_once '../models/SiswaPeriode.php';
require_once '../models/TahunAjaran.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';

// Check if user is admin or guru
if (!in_array($_SESSION['role'], ['admin', 'guru'])) {
    echo "<script>alert('Akses ditolak!'); window.location.href='/absen/';</script>";
    exit;
}

try {
    $siswaModel = new Siswa();
    $siswaPeriodeModel = new SiswaPeriode();
    $tahunAjaranModel = new TahunAjaran();
    $kelasModel = new Kelas();
    $periodeAktifModel = new PeriodeAktif();
} catch (Exception $e) {
    die("Error initializing models: " . $e->getMessage());
}

// Get current active period
$periodeAktifModel->getActive();
$current_tahun_ajaran = $periodeAktifModel->tahun_ajaran ?? '';
$current_semester = $periodeAktifModel->semester ?? '1';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $action = $_POST['action'] ?? '';
    
    if ($action == 'activate_students') {
        $tahun_ajaran = $_POST['tahun_ajaran'];
        $semester = $_POST['semester'];
        $kelas_id = $_POST['kelas_id'];
        $student_ids = $_POST['student_ids'] ?? [];
        
        $success_count = 0;
        $error_count = 0;
        
        foreach ($student_ids as $siswa_id) {
            try {
                // Deactivate current period for this student
                $siswaPeriodeModel->deactivateCurrentPeriod($siswa_id);
                
                // Add student to new period
                $result = $siswaPeriodeModel->addSiswaToNewPeriode(
                    $siswa_id, 
                    $kelas_id, 
                    $tahun_ajaran, 
                    $semester
                );
                
                if ($result) {
                    // Update current period reference in siswa table
                    $siswaModel->updateCurrentPeriod($siswa_id, $tahun_ajaran, $semester);
                    $success_count++;
                } else {
                    $error_count++;
                }
            } catch (Exception $e) {
                $error_count++;
            }
        }
        
        if ($success_count > 0) {
            $_SESSION['success'] = "Berhasil mengaktifkan $success_count siswa di periode $tahun_ajaran - Semester $semester";
        }
        if ($error_count > 0) {
            $_SESSION['error'] = "Gagal mengaktifkan $error_count siswa";
        }
        
        header("Location: manage_periods.php");
        exit;
    }
}

// Get available academic years
try {
    $tahun_ajaran_list = $tahunAjaranModel->getAllAsArray();
} catch (Exception $e) {
    $tahun_ajaran_list = [];
    $_SESSION['error'] = "Error loading academic years: " . $e->getMessage();
}

// Get all classes
try {
    $kelas_list = $kelasModel->getAllAsArray();
} catch (Exception $e) {
    $kelas_list = [];
    $_SESSION['error'] = "Error loading classes: " . $e->getMessage();
}

// Get filter parameters
$filter_tahun_ajaran = $_GET['filter_tahun_ajaran'] ?? $current_tahun_ajaran;
$filter_semester = $_GET['filter_semester'] ?? $current_semester;
$filter_kelas = $_GET['filter_kelas'] ?? '';

// Get students for current filter
$students = null;
if ($filter_tahun_ajaran && $filter_semester) {
    try {
        if ($filter_kelas) {
            $students = $siswaPeriodeModel->getSiswaByPeriodeAndKelas($filter_tahun_ajaran, $filter_semester, $filter_kelas);
        } else {
            $students = $siswaPeriodeModel->getSiswaByPeriode($filter_tahun_ajaran, $filter_semester);
        }
    } catch (Exception $e) {
        $_SESSION['error'] = "Error loading students: " . $e->getMessage();
        $students = null;
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Kelola Periode Akademik Siswa</h1>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali ke Data Siswa
        </a>
    </div>

    <?php if (isset($_SESSION['success'])): ?>
        <div class="alert alert-success alert-dismissible fade show">
            <?= $_SESSION['success'] ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <?php unset($_SESSION['success']); ?>
    <?php endif; ?>

    <?php if (isset($_SESSION['error'])): ?>
        <div class="alert alert-danger alert-dismissible fade show">
            <?= $_SESSION['error'] ?>
            <button type="button" class="close" data-dismiss="alert">&times;</button>
        </div>
        <?php unset($_SESSION['error']); ?>
    <?php endif; ?>

    <!-- Current Active Period Info -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-info-circle"></i> Periode Aktif Saat Ini
            </h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Tahun Ajaran:</strong> <?= htmlspecialchars($current_tahun_ajaran) ?></p>
                    <p><strong>Semester:</strong> <?= $current_semester ?></p>
                </div>
                <div class="col-md-6">
                    <div class="alert alert-info mb-0">
                        <i class="fas fa-lightbulb"></i> 
                        <strong>Tips:</strong> Gunakan halaman ini untuk memindahkan siswa ke periode akademik baru (naik kelas, semester baru, dll.)
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filter Students -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter"></i> Filter Siswa Berdasarkan Periode
            </h6>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-3">
                    <label for="filter_tahun_ajaran" class="form-label">Tahun Ajaran</label>
                    <select name="filter_tahun_ajaran" id="filter_tahun_ajaran" class="form-control" required>
                        <option value="">Pilih Tahun Ajaran</option>
                        <?php foreach ($tahun_ajaran_list as $ta): ?>
                            <option value="<?= $ta['tahun_ajaran'] ?>" <?= $ta['tahun_ajaran'] == $filter_tahun_ajaran ? 'selected' : '' ?>>
                                <?= $ta['tahun_ajaran'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="filter_semester" class="form-label">Semester</label>
                    <select name="filter_semester" id="filter_semester" class="form-control" required>
                        <option value="1" <?= $filter_semester == '1' ? 'selected' : '' ?>>Semester 1</option>
                        <option value="2" <?= $filter_semester == '2' ? 'selected' : '' ?>>Semester 2</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="filter_kelas" class="form-label">Kelas (Opsional)</label>
                    <select name="filter_kelas" id="filter_kelas" class="form-control">
                        <option value="">Semua Kelas</option>
                        <?php foreach ($kelas_list as $kelas): ?>
                            <option value="<?= $kelas['id'] ?>" <?= $kelas['id'] == $filter_kelas ? 'selected' : '' ?>>
                                <?= $kelas['nama_kelas'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <a href="manage_periods.php" class="btn btn-secondary">
                            <i class="fas fa-sync"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Students List and Activation Form -->
    <?php if ($students && $students->rowCount() > 0): ?>
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-users"></i> 
                Siswa di Periode: <?= htmlspecialchars($filter_tahun_ajaran) ?> - Semester <?= $filter_semester ?>
                <?php if ($filter_kelas): ?>
                    - Kelas: <?= htmlspecialchars($kelas_list[array_search($filter_kelas, array_column($kelas_list, 'id'))]['nama_kelas'] ?? '') ?>
                <?php endif; ?>
            </h6>
        </div>
        <div class="card-body">
            <form method="POST" id="activateForm">
                <input type="hidden" name="action" value="activate_students">
                
                <!-- New Period Selection -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <h6><i class="fas fa-arrow-right"></i> Pindahkan Siswa Terpilih ke Periode Baru:</h6>
                            <div class="row">
                                <div class="col-md-3">
                                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran Baru</label>
                                    <select name="tahun_ajaran" id="tahun_ajaran" class="form-control" required>
                                        <option value="">Pilih Tahun Ajaran</option>
                                        <?php foreach ($tahun_ajaran_list as $ta): ?>
                                            <option value="<?= $ta['tahun_ajaran'] ?>"><?= $ta['tahun_ajaran'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label for="semester" class="form-label">Semester Baru</label>
                                    <select name="semester" id="semester" class="form-control" required>
                                        <option value="1">Semester 1</option>
                                        <option value="2">Semester 2</option>
                                    </select>
                                </div>
                                <div class="col-md-3">
                                    <label for="kelas_id" class="form-label">Kelas Baru</label>
                                    <select name="kelas_id" id="kelas_id" class="form-control" required>
                                        <option value="">Pilih Kelas</option>
                                        <?php foreach ($kelas_list as $kelas): ?>
                                            <option value="<?= $kelas['id'] ?>"><?= $kelas['nama_kelas'] ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <label class="form-label">&nbsp;</label>
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-success" onclick="return confirmActivation()">
                                            <i class="fas fa-arrow-right"></i> Pindahkan
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Students Table -->
                <div class="table-responsive">
                    <table class="table table-bordered" id="studentsTable">
                        <thead>
                            <tr>
                                <th width="5%">
                                    <input type="checkbox" id="selectAll" onchange="toggleAll()">
                                </th>
                                <th>No</th>
                                <th>NIS</th>
                                <th>Nama Siswa</th>
                                <th>Jenis Kelamin</th>
                                <th>Kelas Saat Ini</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            $students_array = [];

                            // Convert PDO result to array to avoid issues
                            try {
                                while ($student = $students->fetch(PDO::FETCH_ASSOC)) {
                                    $students_array[] = $student;
                                }
                            } catch (Exception $e) {
                                echo "<tr><td colspan='7'>Error loading students: " . $e->getMessage() . "</td></tr>";
                            }

                            foreach ($students_array as $student):
                                // Validate student data with more robust checking
                                $siswa_id = $student['siswa_id'] ?? $student['id'] ?? '';
                                $nis = $student['nis'] ?? '';
                                $nama_siswa = $student['nama_siswa'] ?? '';
                                $jenis_kelamin = $student['jenis_kelamin'] ?? '';
                                $nama_kelas = $student['nama_kelas'] ?? '';
                                $status = $student['status'] ?? 'aktif';
                                $is_current = $student['is_current'] ?? 0;

                                // Skip if essential data is missing
                                if (empty($siswa_id) || empty($nama_siswa)) {
                                    continue;
                                }
                            ?>
                            <tr>
                                <td>
                                    <input type="checkbox" name="student_ids[]" value="<?= htmlspecialchars($siswa_id) ?>" class="student-checkbox">
                                </td>
                                <td><?= $no++ ?></td>
                                <td><?= htmlspecialchars($nis) ?></td>
                                <td><?= htmlspecialchars($nama_siswa) ?></td>
                                <td><?= $jenis_kelamin == 'L' ? 'Laki-laki' : 'Perempuan' ?></td>
                                <td><?= htmlspecialchars($nama_kelas) ?></td>
                                <td>
                                    <span class="badge badge-<?= $status == 'aktif' ? 'success' : 'secondary' ?>">
                                        <?= ucfirst($status) ?>
                                    </span>
                                    <?php if ($is_current == 1): ?>
                                        <span class="badge badge-primary">Periode Aktif</span>
                                    <?php endif; ?>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </div>
    <?php elseif ($filter_tahun_ajaran && $filter_semester): ?>
    <div class="card shadow mb-4">
        <div class="card-body text-center">
            <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
            <h5>Tidak ada siswa ditemukan</h5>
            <p class="text-muted">Tidak ada siswa di periode <?= htmlspecialchars($filter_tahun_ajaran) ?> - Semester <?= $filter_semester ?></p>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
function toggleAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.student-checkbox');
    
    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAll.checked;
    });
}

function confirmActivation() {
    const checkedBoxes = document.querySelectorAll('.student-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Pilih minimal satu siswa untuk dipindahkan!');
        return false;
    }
    
    const tahunAjaran = document.getElementById('tahun_ajaran').value;
    const semester = document.getElementById('semester').value;
    const kelasSelect = document.getElementById('kelas_id');
    const namaKelas = kelasSelect.options[kelasSelect.selectedIndex].text;
    
    if (!tahunAjaran || !semester || !kelasSelect.value) {
        alert('Lengkapi semua field periode baru!');
        return false;
    }
    
    return confirm(`Yakin ingin memindahkan ${checkedBoxes.length} siswa ke periode ${tahunAjaran} - Semester ${semester} - ${namaKelas}?\n\nTindakan ini akan mengubah periode aktif siswa.`);
}

$(document).ready(function() {
    $('#studentsTable').DataTable({
        "order": [[2, "asc"]], // Sort by NIS
        "columnDefs": [
            {
                "searchable": false,
                "orderable": false,
                "targets": [0, 1] // Checkbox and No columns
            }
        ],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
