/* K13 Report Card Styles */

/* Screen Styles */
.rapor-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 20px;
    font-family: 'Times New Roman', serif;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    border-radius: 5px;
}

.rapor-header {
    text-align: center;
    border-bottom: 2px solid #000;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.rapor-header h3 {
    font-size: 18px;
    font-weight: bold;
    margin: 10px 0;
    color: #333;
}

.rapor-header p {
    font-size: 14px;
    margin: 5px 0;
    color: #666;
}

.rapor-title {
    font-size: 16px;
    font-weight: bold;
    margin: 10px 0;
    color: #000;
}

.student-info {
    margin-bottom: 20px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
}

.student-info table {
    width: 100%;
    border-collapse: collapse;
}

.student-info td {
    padding: 5px;
    vertical-align: top;
    font-size: 14px;
}

.student-info td:first-child {
    font-weight: bold;
    color: #333;
}

.grades-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.grades-table th,
.grades-table td {
    border: 1px solid #333;
    padding: 8px;
    text-align: center;
    font-size: 12px;
}

.grades-table th {
    background-color: #e9ecef;
    font-weight: bold;
    color: #333;
}

.grades-table .subject-name {
    text-align: left;
    padding-left: 10px;
    font-weight: 500;
}

.attendance-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.attendance-table th,
.attendance-table td {
    border: 1px solid #333;
    padding: 8px;
    text-align: center;
    font-size: 12px;
}

.attendance-table th {
    background-color: #e9ecef;
    font-weight: bold;
    color: #333;
}

.character-section {
    margin-bottom: 20px;
}

.character-table {
    width: 100%;
    border-collapse: collapse;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.character-table th,
.character-table td {
    border: 1px solid #333;
    padding: 10px;
    text-align: left;
    font-size: 12px;
}

.character-table th {
    background-color: #e9ecef;
    font-weight: bold;
    color: #333;
}

.section-title {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0 10px 0;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.signature-section {
    margin-top: 40px;
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.signature-box {
    text-align: center;
    width: 30%;
    min-width: 200px;
    margin-bottom: 20px;
}

.signature-line {
    border-bottom: 1px solid #000;
    height: 60px;
    margin: 15px 0 5px 0;
}

.notes-box {
    border: 1px solid #333;
    padding: 15px;
    min-height: 80px;
    font-size: 12px;
    background-color: #f9f9f9;
    border-radius: 3px;
}

.promotion-box {
    border: 1px solid #333;
    padding: 15px;
    font-size: 12px;
    background-color: #f0f8ff;
    border-radius: 3px;
}

/* Action Buttons */
.rapor-actions {
    margin-bottom: 20px;
    text-align: center;
}

.rapor-actions .btn {
    margin: 0 5px;
    padding: 10px 20px;
    font-size: 14px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .rapor-container {
        margin: 10px;
        padding: 15px;
    }
    
    .signature-section {
        flex-direction: column;
    }
    
    .signature-box {
        width: 100%;
        margin-bottom: 30px;
    }
    
    .grades-table,
    .attendance-table,
    .character-table {
        font-size: 10px;
    }
    
    .grades-table th,
    .grades-table td,
    .attendance-table th,
    .attendance-table td,
    .character-table th,
    .character-table td {
        padding: 4px;
    }
}

/* Print Styles */
@media print {
    .no-print { 
        display: none !important; 
    }
    
    .print-only { 
        display: block !important; 
    }
    
    body { 
        font-size: 11px !important; 
        margin: 0 !important;
        padding: 0 !important;
        background: white !important;
        color: black !important;
        font-family: 'Times New Roman', serif !important;
    }
    
    .card, .card-header { 
        border: none !important; 
        box-shadow: none !important; 
        margin: 0 !important;
        padding: 0 !important;
        background: none !important;
    }
    
    .btn, .navbar, .sidebar, .footer { 
        display: none !important; 
    }
    
    .container-fluid { 
        padding: 0 !important; 
        margin: 0 !important; 
    }
    
    .rapor-container { 
        max-width: none !important; 
        margin: 0 !important; 
        padding: 15px !important;
        page-break-inside: avoid;
        box-shadow: none !important;
        border-radius: 0 !important;
    }
    
    .rapor-header {
        page-break-after: avoid;
    }
    
    .student-info {
        background-color: transparent !important;
        padding: 10px 0 !important;
        page-break-after: avoid;
    }
    
    .grades-table, 
    .attendance-table, 
    .character-table {
        page-break-inside: avoid;
        box-shadow: none !important;
    }
    
    .signature-section {
        page-break-inside: avoid;
        margin-top: 30px !important;
        display: table !important;
        width: 100% !important;
    }
    
    .signature-box {
        display: table-cell !important;
        width: 33.33% !important;
        vertical-align: top !important;
    }
    
    /* Force black borders and backgrounds for print */
    .grades-table th, .grades-table td,
    .attendance-table th, .attendance-table td,
    .character-table th, .character-table td {
        border: 1px solid #000 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    .grades-table th,
    .attendance-table th,
    .character-table th {
        background-color: #f0f0f0 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    .notes-box,
    .promotion-box {
        background-color: transparent !important;
        border: 1px solid #000 !important;
    }
    
    /* Page break controls */
    .grades-section, 
    .attendance-section, 
    .extracurricular-section,
    .achievement-section,
    .notes-section,
    .promotion-section {
        page-break-inside: avoid;
    }
    
    /* Ensure proper spacing */
    .section-title {
        margin-top: 15px !important;
        margin-bottom: 8px !important;
        page-break-after: avoid;
    }
}
