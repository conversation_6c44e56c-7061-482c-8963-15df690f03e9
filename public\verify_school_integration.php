<?php
/**
 * Quick verification script for school profile integration
 */

require_once '../config/database.php';
require_once '../models/ProfilSekolah.php';
require_once '../models/Rapor.php';

echo "<h2>School Profile Integration Verification</h2>";

// Test ProfilSekolah model directly
echo "<h3>1. Direct ProfilSekolah Test</h3>";
$profilSekolah = new ProfilSekolah();

if ($profilSekolah->get()) {
    echo "<strong>✓ School profile data found:</strong><br>";
    echo "- School Name: " . ($profilSekolah->nama_sekolah ?: 'Not set') . "<br>";
    echo "- NPSN: " . ($profilSekolah->npsn ?: 'Not set') . "<br>";
    echo "- Address: " . ($profilSekolah->alamat_jalan ?: 'Not set') . "<br>";
    echo "- City: " . ($profilSekolah->kabupaten_kota ?: 'Not set') . "<br>";
    echo "- Principal: " . ($profilSekolah->nama_kepala_sekolah ?: 'Not set') . "<br>";
    echo "- Phone: " . ($profilSekolah->no_telepon ?: 'Not set') . "<br>";
} else {
    echo "<strong>⚠ No school profile data found in database</strong><br>";
    echo "The system will use default fallback values.<br>";
}

// Test Rapor model integration
echo "<h3>2. Rapor Model Integration Test</h3>";
$raporModel = new Rapor();
$schoolInfo = $raporModel->getSchoolInfo();

echo "<strong>School information from Rapor model:</strong><br>";
echo "- School Name: " . $schoolInfo['nama_sekolah'] . "<br>";
echo "- Address: " . $schoolInfo['alamat_sekolah'] . "<br>";
echo "- City: " . $schoolInfo['kota'] . "<br>";
echo "- Principal: " . $schoolInfo['kepala_sekolah'] . "<br>";
echo "- NIP: " . $schoolInfo['nip_kepala_sekolah'] . "<br>";
echo "- Phone: " . $schoolInfo['telepon'] . "<br>";
echo "- Email: " . $schoolInfo['email'] . "<br>";

// Check if data is from database or fallback
$isFromDatabase = ($profilSekolah->get() && !empty($profilSekolah->nama_sekolah));
echo "<br><strong>Data Source: " . ($isFromDatabase ? "✓ Database (profil_sekolah table)" : "⚠ Fallback defaults") . "</strong><br>";

if (!$isFromDatabase) {
    echo "<br><strong>To use real school data:</strong><br>";
    echo "1. Go to the school profile management module in the admin panel<br>";
    echo "2. Fill in the school information<br>";
    echo "3. Save the profile<br>";
    echo "4. Refresh this page to see the updated data<br>";
}

echo "<h3>3. Database Table Check</h3>";
$database = new Database();
$conn = $database->getConnection();

$query = "SHOW TABLES LIKE 'profil_sekolah'";
$stmt = $conn->prepare($query);
$stmt->execute();
$tableExists = $stmt->rowCount() > 0;

echo "- profil_sekolah table: " . ($tableExists ? "✓ Exists" : "✗ Missing") . "<br>";

if ($tableExists) {
    $query = "SELECT COUNT(*) as count FROM profil_sekolah";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $recordCount = $result['count'];
    
    echo "- Records in profil_sekolah: " . $recordCount . "<br>";
    
    if ($recordCount == 0) {
        echo "<strong>⚠ No school profile records found</strong><br>";
        echo "Please add school profile data through the admin interface.<br>";
    }
}

echo "<hr>";
echo "<p><strong>Integration Status: " . ($isFromDatabase ? "✓ Working with real data" : "⚠ Using fallback data") . "</strong></p>";
?>
