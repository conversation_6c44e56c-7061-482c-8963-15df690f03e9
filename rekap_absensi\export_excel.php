<?php
// First line: turn off output buffering
ob_end_clean();

// Start new output buffering
ob_start();

require '../vendor/autoload.php';
require_once '../models/Absensi.php';
require_once '../models/PeriodeAktif.php';
session_start();

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

try {
    // Get active period and selected period
    $periode = new PeriodeAktif();
    $periode->getActive();

    // Get semester and tahun ajaran from GET or use active period
    $semester = isset($_GET['semester']) ? $_GET['semester'] : $periode->semester;
    $tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periode->tahun_ajaran;

    // Check if siswa_id is provided
    if (!isset($_GET['siswa_id'])) {
        die("Parameter siswa tidak ditemukan.");
    }

    $absensi = new Absensi();
    $detail_data = $absensi->getRekapDetailSiswa($_GET['siswa_id'], $semester, $tahun_ajaran);

    // Get first row for student info
    $student_info = $detail_data->fetch(PDO::FETCH_ASSOC);
    if (!$student_info) {
        die("Data siswa tidak ditemukan.");
    }
    // Reset pointer to beginning
    $detail_data->execute();

    // Create new Spreadsheet object
    $spreadsheet = new Spreadsheet();

    // Set document properties
    $spreadsheet->getProperties()
        ->setCreator('Sistem Absensi')
        ->setLastModifiedBy('Sistem Absensi')
        ->setTitle('Detail Absensi Siswa')
        ->setSubject('Detail Absensi ' . $student_info['nama_siswa'])
        ->setDescription('Detail Absensi untuk siswa ' . $student_info['nama_siswa']);

    // Get active sheet
    $sheet = $spreadsheet->getActiveSheet();

    // Set column widths
    $sheet->getColumnDimension('A')->setWidth(5);  // No
    $sheet->getColumnDimension('B')->setWidth(15); // Tanggal
    $sheet->getColumnDimension('C')->setWidth(40); // Mata Pelajaran
    $sheet->getColumnDimension('D')->setWidth(25); // Guru
    $sheet->getColumnDimension('E')->setWidth(10); // Status
    $sheet->getColumnDimension('F')->setWidth(20); // Waktu

    // Student info header
    $sheet->setCellValue('A1', 'Nama Siswa');
    $sheet->setCellValue('B1', ': ' . $student_info['nama_siswa']);
    $sheet->setCellValue('A2', 'NIS');
    $sheet->setCellValue('B2', ': ' . $student_info['nis']);
    $sheet->setCellValue('A3', 'Kelas');
    $sheet->setCellValue('B3', ': ' . $student_info['nama_kelas']);

    // Style student info
    $sheet->getStyle('A1:A3')->getFont()->setBold(true);
    $sheet->mergeCells('B1:F1');
    $sheet->mergeCells('B2:F2');
    $sheet->mergeCells('B3:F3');

    // Add table headers
    $sheet->setCellValue('A5', 'No');
    $sheet->setCellValue('B5', 'Tanggal');
    $sheet->setCellValue('C5', 'Mata Pelajaran');
    $sheet->setCellValue('D5', 'Guru');
    $sheet->setCellValue('E5', 'Status');
    $sheet->setCellValue('F5', 'Waktu');

    // Style the table headers
    $sheet->getStyle('A5:F5')->applyFromArray([
        'font' => ['bold' => true],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => 'E2EFDA']
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN
            ]
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ]);

    // Add data
    $row = 6;
    $no = 1;

    while ($data = $detail_data->fetch(PDO::FETCH_ASSOC)) {
        $sheet->setCellValue('A' . $row, $no++);
        $sheet->setCellValue('B' . $row, date('d/m/Y', strtotime($data['tanggal_absensi'])));
        $sheet->setCellValue('C' . $row, $data['nama_mapel']);
        $sheet->setCellValue('D' . $row, $data['nama_guru']);
        $sheet->setCellValue('E' . $row, $data['status']);
        $sheet->setCellValue('F' . $row, $data['waktu']);

        // Color code the status
        $statusColor = '';
        switch (strtolower($data['status'])) {
            case 'hadir':
                $statusColor = '28a745';
                break;
            case 'sakit':
                $statusColor = 'ffc107';
                break;
            case 'izin':
                $statusColor = '17a2b8';
                break;
            case 'alpha':
                $statusColor = 'dc3545';
                break;
        }
        if ($statusColor) {
            $sheet->getStyle('E' . $row)->getFont()->getColor()->setRGB($statusColor);
        }

        $row++;
    }

    // Style all data
    $lastRow = $row - 1;
    $dataRange = 'A5:F' . $lastRow;
    
    // Borders
    $sheet->getStyle($dataRange)->applyFromArray([
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN
            ]
        ]
    ]);

    // Alignment
    $sheet->getStyle('A6:A' . $lastRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('B6:B' . $lastRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('E6:E' . $lastRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('F6:F' . $lastRow)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

    // Add signature
    $row = $lastRow + 2;
    $sheet->setCellValue('F' . $row, date('d/m/Y'));
    $row++;
    $sheet->setCellValue('F' . $row, 'Wali Kelas');
    $row += 2;
    $sheet->setCellValue('F' . $row, $student_info['wali_kelas']);

    // Set filename
    $filename = 'Detail_Absensi_';
    $filename .= preg_replace('/[^a-zA-Z0-9]/', '_', $student_info['nama_siswa']) . '_';
    $filename .= date('Y-m-d_H-i-s') . '.xlsx';

    // Clear any previous output and set headers
    if (ob_get_length()) ob_end_clean();
    
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');
    header('Cache-Control: max-age=1');
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
    header('Cache-Control: cache, must-revalidate');
    header('Pragma: public');

    // Output file to browser
    $writer = new Xlsx($spreadsheet);
    $writer->save('php://output');
    exit();

} catch (Exception $e) {
    error_log('Excel Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file. Silakan coba lagi. Error: " . $e->getMessage());
}
