<?php
/**
 * Test script for K13 Report Card improvements
 * This script tests the three main improvements:
 * 1. School data integration
 * 2. Grade display logic
 * 3. Class promotion decision logic
 */

require_once '../config/database.php';
require_once '../models/Rapor.php';
require_once '../models/ProfilSekolah.php';
require_once '../models/Tingkat.php';

echo "<h2>K13 Report Card Improvements Test</h2>";

// Test 1: School Data Integration
echo "<h3>1. School Data Integration Test</h3>";
$raporModel = new Rapor();
$schoolInfo = $raporModel->getSchoolInfo();

echo "<strong>School Information Retrieved:</strong><br>";
echo "- School Name: " . $schoolInfo['nama_sekolah'] . "<br>";
echo "- Address: " . $schoolInfo['alamat_sekolah'] . "<br>";
echo "- Principal: " . $schoolInfo['kepala_sekolah'] . "<br>";
echo "- NIP: " . $schoolInfo['nip_kepala_sekolah'] . "<br>";
echo "- Phone: " . $schoolInfo['telepon'] . "<br>";
echo "- Email: " . $schoolInfo['email'] . "<br>";

// Test 2: Grade Display Logic
echo "<h3>2. Grade Display Logic Test</h3>";
echo "<strong>Testing various grade values:</strong><br>";

$testGrades = [
    ['nilai' => null, 'kkm' => 75, 'description' => 'Null grade'],
    ['nilai' => '', 'kkm' => 75, 'description' => 'Empty string grade'],
    ['nilai' => 0, 'kkm' => 75, 'description' => 'Zero grade'],
    ['nilai' => 95, 'kkm' => 75, 'description' => 'Excellent grade (95)'],
    ['nilai' => 85, 'kkm' => 75, 'description' => 'Good grade (85)'],
    ['nilai' => 75, 'kkm' => 75, 'description' => 'Minimum passing grade (75)'],
    ['nilai' => 65, 'kkm' => 75, 'description' => 'Below minimum grade (65)']
];

foreach ($testGrades as $test) {
    $result = $raporModel->getGradeDescription($test['nilai'], $test['kkm']);
    echo "- {$test['description']}: Predicate = '{$result['predikat']}', Description = '{$result['deskripsi']}'<br>";
}

// Test 3: Class Promotion Decision Logic
echo "<h3>3. Class Promotion Decision Logic Test</h3>";

// Test with a sample student ID (you may need to adjust this based on your data)
echo "<strong>Testing final grade detection:</strong><br>";
echo "Note: This test requires actual student data in the database.<br>";

// Test the method with different tingkat names
$testTingkatNames = [
    '10' => false,
    '11' => false,
    '12' => true,
    'X' => false,
    'XI' => false,
    'XII' => true,
    'Kelas 10' => false,
    'Kelas 11' => false,
    'Kelas 12' => true,
    'Grade 10' => false,
    'Grade 11' => false,
    'Grade 12' => true
];

echo "<strong>Testing grade level detection logic:</strong><br>";
foreach ($testTingkatNames as $tingkat => $expected) {
    $isFinal = (strpos(strtolower($tingkat), '12') !== false || 
                strpos(strtolower($tingkat), 'xii') !== false || 
                strpos(strtolower($tingkat), 'kelas 12') !== false ||
                strpos(strtolower($tingkat), 'grade 12') !== false);
    
    $status = $isFinal ? "✓ Final Grade" : "✗ Not Final Grade";
    $correct = ($isFinal === $expected) ? "✓" : "✗";
    echo "- '{$tingkat}': {$status} {$correct}<br>";
}

echo "<h3>4. Integration Status</h3>";
echo "<strong>Required Models:</strong><br>";
echo "- Rapor Model: " . (class_exists('Rapor') ? "✓ Available" : "✗ Missing") . "<br>";
echo "- ProfilSekolah Model: " . (class_exists('ProfilSekolah') ? "✓ Available" : "✗ Missing") . "<br>";
echo "- Tingkat Model: " . (class_exists('Tingkat') ? "✓ Available" : "✗ Missing") . "<br>";

// Test ProfilSekolah method availability
if (class_exists('ProfilSekolah')) {
    $testProfil = new ProfilSekolah();
    echo "- ProfilSekolah->get() method: " . (method_exists($testProfil, 'get') ? "✓ Available" : "✗ Missing") . "<br>";
}

echo "<strong>Database Tables:</strong><br>";
$database = new Database();
$conn = $database->getConnection();

$tables = ['profil_sekolah', 'tingkat', 'kelas', 'siswa'];
foreach ($tables as $table) {
    $query = "SHOW TABLES LIKE '$table'";
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $exists = $stmt->rowCount() > 0;
    echo "- {$table}: " . ($exists ? "✓ Exists" : "✗ Missing") . "<br>";
}

echo "<h3>5. Recommendations</h3>";
echo "<strong>To fully test the improvements:</strong><br>";
echo "1. Ensure school profile data is entered in the profil_sekolah table<br>";
echo "2. Create test students in different grade levels (10, 11, 12)<br>";
echo "3. Test report card generation for students in each grade level<br>";
echo "4. Verify that grade 12 students don't show promotion decisions<br>";
echo "5. Test with students having missing grades to see '-' display<br>";

echo "<hr>";
echo "<p><strong>Test completed!</strong> Check the results above to verify the improvements are working correctly.</p>";
?>
