<?php
require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Guru.php';
require_once '../models/User.php';
require_once '../models/Nilai.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Tugas.php';
require_once '../models/NilaiTugas.php';

use Dompdf\Dompdf;
use Dompdf\Options;

session_start();

// Check if user is logged in and is a guru
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'guru') {
    header("Location: /absen/403.php");
    exit();
}

// Get guru_id from user_id
$user = new User();
$guru_id = $user->getGuruId($_SESSION['user_id']);

if (!$guru_id) {
    $_SESSION['error'] = "Data guru tidak ditemukan.";
    header("Location: /absen/index.php");
    exit();
}

// Check if guru is wali kelas
$guru = new Guru();
if (!$guru->isWaliKelas($guru_id)) {
    header("Location: /absen/403.php");
    exit();
}

// Get active period and selected period
$periode = new PeriodeAktif();
$periode->getActive();

// Get semester and tahun ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : $periode->semester;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periode->tahun_ajaran;

$nilai = new Nilai();
$mapel = new MataPelajaran();
$tugas = new Tugas();
$nilaiTugas = new NilaiTugas();
$kelas = new Kelas();

// Get selected class and verify it belongs to this wali kelas
$selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
if (!$selected_kelas) {
    die("Kelas tidak dipilih.");
}

$valid_kelas = false;
$kelas_list = $guru->getKelasAsWaliKelas($guru_id);
while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
    if ($row['id'] == $selected_kelas) {
        $valid_kelas = true;
        $nama_kelas = $row['nama_kelas'];
        break;
    }
}
if (!$valid_kelas) {
    header("Location: /absen/403.php");
    exit();
}

// Get selected mapel
$selected_mapel = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : 'all';

try {
    // Prevent any unwanted output
    ob_clean();

    // Initialize DomPDF
    $options = new Options();
    $options->set('isHtml5ParserEnabled', true);
    $options->set('isPhpEnabled', true);

    $dompdf = new Dompdf($options);
    $dompdf->setPaper('A4', 'portrait');

    // Start building HTML
    $html = '
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; }
            table { width: 100%; border-collapse: collapse; margin-top: 10px; margin-bottom: 20px; }
            th, td { border: 1px solid #000; padding: 5px; font-size: 12px; }
            th { background-color: #f0f0f0; }
            .header { text-align: center; margin-bottom: 20px; }
            .header h2 { margin: 0; }
            .header p { margin: 5px 0; }
            .mapel-header { margin-top: 20px; margin-bottom: 10px; font-weight: bold; }
            .page-break { page-break-after: always; }
        </style>
    </head>
    <body>
        <div class="header">
            <h2>REKAP NILAI SISWA</h2>
            <p>Kelas: '.$nama_kelas.'</p>
            <p>Periode: '.$semester.' - '.$tahun_ajaran.'</p>
        </div>';

    // Get all mapel for this class
    $mapel_list = $mapel->getByKelas($selected_kelas);
    $mapel_data = [];
    while ($m = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
        if ($selected_mapel == 'all' || $selected_mapel == $m['id']) {
            $mapel_data[] = $m;
        }
    }

    // Get student list
    $siswa_list = $nilai->getSiswaByKelas($selected_kelas);
    $siswa_data = [];
    while ($s = $siswa_list->fetch(PDO::FETCH_ASSOC)) {
        $siswa_data[] = $s;
    }

    // Start building HTML content for each mapel
    foreach ($mapel_data as $m) {
        $html .= '
        <div class="mapel-header">
            <h3>' . $m['nama_mapel'] . ' (KKM: ' . $m['kkm'] . ')</h3>
        </div>
        <table>
            <thead>
                <tr>
                    <th>No</th>
                    <th>NIS</th>
                    <th>Nama Siswa</th>
                    <th>Rata-rata Tugas</th>
                    <th>UTS</th>
                    <th>UAS</th>
                    <th>Absensi</th>
                    <th>Rumus</th>
                    <th>Nilai Akhir</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>';

        $no = 1;
        foreach ($siswa_data as $siswa) {
            $nilai_data = $nilai->getNilaiSiswa($siswa['id'], $m['id'], $semester, $tahun_ajaran);
            $rata_tugas = $nilaiTugas->getRataTugas($siswa['id'], $m['id'], $semester, $tahun_ajaran);
            $nilai_akhir = $nilai_data['nilai_akhir'] ?? 0;
            $status = $nilai_akhir >= $m['kkm'] ? 'Tuntas' : 'Belum Tuntas';
            $color = $nilai_akhir >= $m['kkm'] ? 'green' : 'red';

            $html .= '<tr>
                <td>' . $no++ . '</td>
                <td>' . $siswa['nis'] . '</td>
                <td>' . $siswa['nama_siswa'] . '</td>
                <td>' . ($rata_tugas ? number_format($rata_tugas, 2) : '-') . '</td>
                <td>' . ($nilai_data['nilai_uts'] ?? '-') . '</td>
                <td>' . ($nilai_data['nilai_uas'] ?? '-') . '</td>
                <td>' . ($nilai_data['nilai_absen'] ?? '-') . '</td>
                <td>' . ($nilai_data['rumus_nilai'] ?? '-') . '</td>
                <td style="color: ' . $color . '; font-weight: bold;">' . ($nilai_akhir ?: '-') . '</td>
                <td style="color: ' . $color . ';">' . $status . '</td>
            </tr>';
        }

        $html .= '</tbody></table>';
        if (next($mapel_data)) {
            $html .= '<div class="page-break"></div>';
        }
    }

    $html .= '</body></html>';

    // Load HTML to DomPDF
    $dompdf->loadHtml($html);

    // Render the PDF
    $dompdf->render();

    // Output the generated PDF
    $dompdf->stream('Rekap_Nilai_'.$nama_kelas.'_'.$semester.'_'.$tahun_ajaran.'.pdf', [
        'Attachment' => true
    ]);

} catch (Exception $e) {
    error_log('PDF Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file PDF. Silakan coba lagi.");
}
